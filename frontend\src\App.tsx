/**
 * Main TradingView Frontend Application
 * Provides React Query context and routing for trading dashboard
 */

// React is not needed as a value import in this file
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import Dashboard from './pages/Dashboard';
import ErrorBoundary from './components/ui/ErrorBoundary';
import { useApiStatus } from './hooks/useApi';
import './index.css';

// Create React Query client with optimized settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error: unknown) => {
        // Don't retry on 4xx errors
        const status = (error as { response?: { status?: number } }).response?.status;
        if (typeof status === 'number' && status >= 400 && status < 500) {
          return false;
        }
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
    },
  },
});

/**
 * Connection status indicator component
 */
function ConnectionStatus() {
  const { isOnline, lastChecked } = useApiStatus();
  
  return (
    <div className={`fixed top-4 right-4 z-50 px-3 py-2 rounded-lg text-sm font-medium ${
      isOnline 
        ? 'bg-green-100 text-green-800 border border-green-200' 
        : 'bg-red-100 text-red-800 border border-red-200'
    }`}>
      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${
          isOnline ? 'bg-green-500' : 'bg-red-500'
        }`} />
        <span>
          {isOnline ? 'Connected' : 'Disconnected'}
        </span>
      </div>
      <div className="text-xs opacity-75 mt-1">
        Last checked: {lastChecked.toLocaleTimeString()}
      </div>
    </div>
  );
}

/**
 * Main App component with providers and error handling
 */
function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <ConnectionStatus />
          <Dashboard />
        </div>
        
        {/* React Query DevTools - only in development */}
        {import.meta.env.DEV && (
          <ReactQueryDevtools 
            initialIsOpen={false} 
            position="bottom"
          />
        )}
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
