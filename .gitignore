# Environment variables
.env
.env.local
.env.production
.env.staging

# Virtual environment
venv/
venv-win/
.venv/
env/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Logs
*.log
logs/
trading_bot.log

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Railway (if used)
.railway/

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/

# Temporary files
*.tmp
*.temp
.cache/

# Deployment
*.pid
*.seed
*.pid.lock