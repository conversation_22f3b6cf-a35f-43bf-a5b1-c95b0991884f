/**
 * System Status Card Component
 * Displays backend system health, configuration, and operational status
 */

// React is not needed as a value import in this file
import { Server, CheckCircle, XCircle, AlertCircle, Clock, Database } from 'lucide-react';
import type { SystemStatus, TradingConfig } from '../../types/api';

interface SystemStatusCardProps {
  status: SystemStatus | undefined;
  config: TradingConfig | undefined;
  className?: string;
}

function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  if (days > 0) return `${days}d ${hours}h ${minutes}m`;
  if (hours > 0) return `${hours}h ${minutes}m`;
  return `${minutes}m`;
}

function getStatusIndicator(health: SystemStatus['system_health'] | undefined) {
  switch (health) {
    case 'healthy':
      return { color: 'text-green-600', bgColor: 'bg-green-100 dark:bg-green-900/20', icon: CheckCircle, label: 'Healthy' };
    case 'warning':
      return { color: 'text-yellow-600', bgColor: 'bg-yellow-100 dark:bg-yellow-900/20', icon: AlertCircle, label: 'Warning' };
    case 'error':
      return { color: 'text-red-600', bgColor: 'bg-red-100 dark:bg-red-900/20', icon: XCircle, label: 'Error' };
    default:
      return { color: 'text-gray-600', bgColor: 'bg-gray-100 dark:bg-gray-900/20', icon: AlertCircle, label: 'Unknown' };
  }
}

function SystemStatusCard({ status, config, className = '' }: SystemStatusCardProps) {
  const statusIndicator = getStatusIndicator(status?.system_health);
  const StatusIcon = statusIndicator.icon;

  return (
    <div className={`trading-card ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Server className="h-5 w-5 text-blue-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">System Status</h3>
        </div>

        <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${statusIndicator.bgColor} ${statusIndicator.color}`}>
          <StatusIcon className="h-4 w-4" />
          <span>{statusIndicator.label}</span>
        </div>
      </div>

      <div className="space-y-4">
        {/* API Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <div className="flex items-center space-x-3">
            <Database className="h-5 w-5 text-blue-500" />
            <div>
              <p className="font-medium text-gray-900 dark:text-white">API Connection</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Backend service status</p>
            </div>
          </div>
          <div className="text-right">
            <p className={`font-semibold ${status?.api_connected ? 'text-green-600' : 'text-red-600'}`}>
              {status?.api_connected ? 'Connected' : 'Disconnected'}
            </p>
            {status?.last_heartbeat && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {new Date(status.last_heartbeat).toLocaleTimeString()}
              </p>
            )}
          </div>
        </div>

        {/* Trading Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <div className="flex items-center space-x-3">
            <Clock className="h-5 w-5 text-purple-500" />
            <div>
              <p className="font-medium text-gray-900 dark:text-white">Trading Engine</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Automated trading status</p>
            </div>
          </div>
          <div className="text-right">
            <p className={`font-semibold ${config?.enabled ? 'text-green-600' : 'text-yellow-600'}`}>
              {config?.enabled ? 'Enabled' : 'Disabled'}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">{config?.allowed_symbols?.length || 0} pairs</p>
          </div>
        </div>

        {/* System Uptime */}
        {typeof status?.uptime === 'number' && (
          <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div className="flex items-center space-x-3">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="font-medium text-gray-900 dark:text-white">System Uptime</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Continuous operation time</p>
              </div>
            </div>
            <div className="text-right">
              <p className="font-semibold text-gray-900 dark:text-white">{formatUptime(status.uptime)}</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">Since last restart</p>
            </div>
          </div>
        )}
      </div>

      {config && (
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Configuration Summary</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-600 dark:text-gray-400">Max Position</p>
              <p className="font-semibold text-gray-900 dark:text-white">{config.max_position_size ?? 'Not set'}</p>
            </div>
            <div>
              <p className="text-gray-600 dark:text-gray-400">Risk %</p>
              <p className="font-semibold text-gray-900 dark:text-white">{config.risk_percentage}%</p>
            </div>
            <div>
              <p className="text-gray-600 dark:text-gray-400">Stop Loss %</p>
              <p className="font-semibold text-gray-900 dark:text-white">
                {typeof config.stop_loss_percentage === 'number' ? `${config.stop_loss_percentage}%` : config.stop_loss_percentage}
              </p>
            </div>
            <div>
              <p className="text-gray-600 dark:text-gray-400">Take Profit %</p>
              <p className="font-semibold text-gray-900 dark:text-white">
                {typeof config.take_profit_percentage === 'number' ? `${config.take_profit_percentage}%` : config.take_profit_percentage}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">Last updated: {new Date().toLocaleString()}</p>
      </div>
    </div>
  );
}

export default SystemStatusCard;