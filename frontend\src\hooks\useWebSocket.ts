/**
 * React Hook for WebSocket Real-time Data Integration
 * Provides seamless WebSocket connectivity with React components
 */

import { useEffect, useRef, useCallback, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import webSocketService from '../services/websocket';
import type {
  WebSocketEventType,
  PriceUpdateMessage,
  BalanceUpdateMessage,
  TradeUpdateMessage,
  PnLUpdateMessage,
} from '../services/websocket';
import { QUERY_KEYS } from './useApi';
import { useWebSocketErrorHandler } from './useErrorHandler';
import type { TradeHistoryItem } from '../types/api';
import type { DependencyList } from 'react';

interface UseWebSocketOptions {
  autoConnect?: boolean;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: unknown) => void;
}

interface WebSocketState {
  connected: boolean;
  connecting: boolean;
  error: string | null;
}

/**
 * Main WebSocket hook for connection management
 */
export function useWebSocket(options: UseWebSocketOptions = {}) {
  const { autoConnect = true, onConnect, onDisconnect, onError } = options;
  const [state, setState] = useState<WebSocketState>({
    connected: false,
    connecting: false,
    error: null,
  });
  const queryClient = useQueryClient();
  const isInitialized = useRef(false);
  const { handleWebSocketError, clearError } = useWebSocketErrorHandler();

  const connect = useCallback(async () => {
    try {
      setState((prev: WebSocketState) => ({ ...prev, connecting: true, error: null }));
      await webSocketService.connect();
    } catch (error) {
      handleWebSocketError(error, 'WebSocket Connection');
      const errorMessage = error instanceof Error ? error.message : 'Connection failed';
      setState((prev: WebSocketState) => ({ ...prev, error: errorMessage, connecting: false }));
      onError?.(error);
    }
  }, [onError, handleWebSocketError]);

  const disconnect = useCallback(() => {
    webSocketService.disconnect();
  }, []);

  useEffect(() => {
    if (isInitialized.current) return;
    isInitialized.current = true;

    // Subscribe to connection status changes
    const unsubscribeConnection = webSocketService.onConnectionChange((connected) => {
      setState((prev: WebSocketState) => ({ 
        ...prev, 
        connected, 
        connecting: false,
        error: connected ? null : prev.error 
      }));
      
      if (connected) {
        clearError();
        // Refresh critical queries on reconnect
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.SYSTEM_STATUS });
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.ACCOUNT_BALANCES });
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.PNL_DATA });
        onConnect?.();
      } else {
        onDisconnect?.();
      }
    });

    // Auto-connect if enabled
    if (autoConnect) {
      connect();
    }

    return () => {
      unsubscribeConnection();
      // Don't auto-disconnect on cleanup to prevent connection cycling
      // The WebSocket service manages its own lifecycle
    };
  }, [autoConnect, connect, disconnect, onConnect, onDisconnect, clearError, queryClient]);

  return {
    ...state,
    connect,
    disconnect,
  };
}

/**
 * Hook for subscribing to real-time balance updates
 */
export function useRealtimeBalances() {
  const queryClient = useQueryClient();
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    const unsubscribe = webSocketService.subscribe('balance', (data: BalanceUpdateMessage['data']) => {
      // Update React Query cache with new balance data
      queryClient.setQueryData(QUERY_KEYS.ACCOUNT_BALANCES, data);
      setLastUpdate(new Date());
    });

    return unsubscribe;
  }, [queryClient]);

  return { lastUpdate };
}

/**
 * Hook for subscribing to real-time trade updates
 */
export function useRealtimeTrades() {
  const queryClient = useQueryClient();
  const [lastTrade, setLastTrade] = useState<TradeHistoryItem | TradeHistoryItem[] | null>(null);

  useEffect(() => {
    const unsubscribe = webSocketService.subscribe('trade', (data: TradeUpdateMessage['data']) => {
      // Update trade history cache and P&L data cache by invalidation to fetch authoritative data
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.TRADE_HISTORY });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.PNL_DATA });
      setLastTrade(data);
    });

    return unsubscribe;
  }, [queryClient]);

  return { lastTrade };
}

/**
 * Hook for subscribing to real-time price updates
 */
export function useRealtimePrices() {
  const [prices, setPrices] = useState<Record<string, number>>({});
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    const unsubscribe = webSocketService.subscribe('price', (data: PriceUpdateMessage['data']) => {
      // Normalize various price payload shapes to a flat symbol->price map
      let normalized: Record<string, number> = {};
      if (Array.isArray(data)) {
        normalized = Object.fromEntries(data.map(({ symbol, price }) => [symbol, price]));
      } else if (typeof data === 'object' && data !== null) {
        if ('symbol' in data && 'price' in data) {
          const single = data as { symbol: string; price: number };
          normalized = { [single.symbol]: single.price };
        } else {
          normalized = data as Record<string, number>;
        }
      }

      setPrices((prev: Record<string, number>) => ({ ...prev, ...normalized }));
      setLastUpdate(new Date());
    });

    return unsubscribe;
  }, []);

  return { prices, lastUpdate };
}

/**
 * Hook for subscribing to real-time system status updates
 */
export function useRealtimeSystemStatus() {
  const queryClient = useQueryClient();
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    // Invalidate system status when websocket connection changes
    const unsubscribe = webSocketService.onConnectionChange(() => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.SYSTEM_STATUS });
      setLastUpdate(new Date());
    });

    return unsubscribe;
  }, [queryClient]);

  return { lastUpdate };
}

/**
 * Hook for subscribing to real-time P&L updates
 */
export function useRealtimeProfitLoss() {
  const queryClient = useQueryClient();
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    const unsubscribe = webSocketService.subscribe('pnl', () => {
      // Invalidate REST-driven P&L query to fetch authoritative snapshot
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.PNL_DATA });
      setLastUpdate(new Date());
    });

    return unsubscribe;
  }, [queryClient]);

  return { lastUpdate };
}

/**
 * Hook for sending WebSocket messages
 */
export function useWebSocketSender() {
  const send = useCallback((type: WebSocketEventType, data: unknown) => {
    webSocketService.send(type, data);
  }, []);

  return { send };
}

/**
 * Hook for custom WebSocket event subscriptions (typed by event)
 */
export function useWebSocketSubscription<T extends WebSocketEventType>(
  eventType: T,
  callback: (
    data: T extends 'price' ? PriceUpdateMessage['data'] :
      T extends 'balance' ? BalanceUpdateMessage['data'] :
      T extends 'trade' ? TradeUpdateMessage['data'] :
      T extends 'pnl' ? PnLUpdateMessage['data'] :
      { connected: boolean; message?: string }
  ) => void,
  deps: DependencyList = []
) {
  // Keep the latest callback without forcing re-subscription on each render
  const callbackRef = useRef(callback);
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    const unsubscribe = webSocketService.subscribe(eventType, (data: unknown) => {
      // Invoke the latest callback stored in ref
      (callbackRef.current as (arg: unknown) => void)(data);
    });
    return unsubscribe;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventType, ...deps]);
}

/**
 * Hook that combines all real-time subscriptions for comprehensive updates
 */
export function useRealtimeData() {
  const balances = useRealtimeBalances();
  const trades = useRealtimeTrades();
  const prices = useRealtimePrices();
  const systemStatus = useRealtimeSystemStatus();
  const profitLoss = useRealtimeProfitLoss();
  const webSocket = useWebSocket();

  const tradeLastTime = Array.isArray(trades.lastTrade)
    ? Math.max(0, ...trades.lastTrade.map(t => new Date(t.timestamp).getTime()))
    : (trades.lastTrade ? new Date(trades.lastTrade.timestamp).getTime() : 0);

  return {
    webSocket,
    balances,
    trades,
    prices,
    systemStatus,
    profitLoss,
    isConnected: webSocket.connected,
    lastActivity: Math.max(
      balances.lastUpdate?.getTime() || 0,
      tradeLastTime,
      prices.lastUpdate?.getTime() || 0,
      systemStatus.lastUpdate?.getTime() || 0,
      profitLoss.lastUpdate?.getTime() || 0
    ),
  };
}