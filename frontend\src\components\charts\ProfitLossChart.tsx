/**
 * ProfitLossChart Component
 * 
 * Displays comprehensive profit/loss visualization with multiple chart types,
 * time period filtering, and detailed analytics using Chart.js.
 */

import React, { useState, useMemo } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';
import type { ChartOptions, TooltipItem, ScriptableScaleContext } from 'chart.js';
import { TrendingUp, TrendingDown, BarChart3, LineChart, Calendar, DollarSign } from 'lucide-react';
import { usePnLData } from '../../hooks/useApi';
import { LoadingSpinner, InlineSpinner } from '../ui/LoadingSpinner';
import type { PnLHistoryItem } from '../../types/api';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Toolt<PERSON>,
  <PERSON>,
  Filler
);

interface ProfitLossChartProps {
  className?: string;
  height?: number;
}

type ChartType = 'line' | 'bar';
type TimePeriod = '24h' | '7d' | '30d' | '90d' | 'all';

const ProfitLossChart: React.FC<ProfitLossChartProps> = ({ className = '', height = 400 }) => {
  const [chartType, setChartType] = useState<ChartType>('line');
  const [timePeriod, setTimePeriod] = useState<TimePeriod>('7d');
  const [showCumulative, setShowCumulative] = useState(true);

  const { data: plData, isLoading, error, refetch, isRefetching } = usePnLData();

  // Filter data based on time period
  const filteredData = useMemo(() => {
    if (!plData || !plData.pnl) return [];

    const pnlArray = plData.pnl;
    const now = new Date();
    const cutoffDate = new Date();

    switch (timePeriod) {
      case '24h':
        cutoffDate.setHours(now.getHours() - 24);
        break;
      case '7d':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        cutoffDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        cutoffDate.setDate(now.getDate() - 90);
        break;
      case 'all':
      default:
        return pnlArray;
    }

    return pnlArray.filter((item: PnLHistoryItem) => new Date(item.timestamp) >= cutoffDate);
  }, [plData, timePeriod]);

  // Calculate cumulative P&L and statistics
  const chartData = useMemo(() => {
    if (!filteredData.length) return null;

    const labels = filteredData.map((item: PnLHistoryItem) => {
      const date = new Date(item.timestamp);
      if (timePeriod === '24h') {
        return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
      } else {
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      }
    });

    let cumulativePnL = 0;
    const realizedPnL = filteredData.map((item: PnLHistoryItem) => item.realized_pnl);
    const unrealizedPnL = filteredData.map((item: PnLHistoryItem) => item.unrealized_pnl);
    const totalPnL = filteredData.map((item: PnLHistoryItem) => item.total_pnl);
    const cumulativeData = filteredData.map((item: PnLHistoryItem) => {
      cumulativePnL += item.total_pnl;
      return cumulativePnL;
    });

    const datasets = [];

    if (showCumulative) {
      datasets.push({
        label: 'Cumulative Total P&L',
        data: cumulativeData,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true,
        tension: 0.4,
        pointRadius: chartType === 'line' ? 3 : 0,
        pointHoverRadius: 6,
      });
    }

    // Add realized P&L line
    datasets.push({
      label: 'Realized P&L',
      data: realizedPnL,
      borderColor: 'rgb(34, 197, 94)',
      backgroundColor: 'rgba(34, 197, 94, 0.1)',
      fill: false,
      tension: 0.4,
      pointRadius: chartType === 'line' ? 2 : 0,
      pointHoverRadius: 5,
    });

    // Add unrealized P&L line
    datasets.push({
      label: 'Unrealized P&L',
      data: unrealizedPnL,
      borderColor: 'rgb(168, 85, 247)',
      backgroundColor: 'rgba(168, 85, 247, 0.1)',
      fill: false,
      tension: 0.4,
      pointRadius: chartType === 'line' ? 2 : 0,
      pointHoverRadius: 5,
    });

    // Add total P&L line
    if (!showCumulative) {
      datasets.push({
        label: 'Total P&L',
        data: totalPnL,
        borderColor: totalPnL.map((val: number) => val >= 0 ? 'rgb(34, 197, 94)' : 'rgb(239, 68, 68)'),
        backgroundColor: totalPnL.map((val: number) => val >= 0 ? 'rgba(34, 197, 94, 0.2)' : 'rgba(239, 68, 68, 0.2)'),
        fill: false,
        tension: 0.4,
        pointRadius: chartType === 'line' ? 2 : 0,
        pointHoverRadius: 5,
      });
    }

    return {
      labels,
      datasets
    };
  }, [filteredData, showCumulative, chartType, timePeriod]);

  // Calculate statistics
  const statistics = useMemo(() => {
    if (!filteredData.length) return null;

    const totalPnL = filteredData.reduce((sum: number, item: PnLHistoryItem) => sum + item.realized_pnl, 0);
    const profitableTrades = filteredData.filter((item: PnLHistoryItem) => item.realized_pnl > 0);
    const losingTrades = filteredData.filter((item: PnLHistoryItem) => item.realized_pnl < 0);
    
    const winRate = filteredData.length > 0 ? (profitableTrades.length / filteredData.length) * 100 : 0;
    const avgWin = profitableTrades.length > 0 ? profitableTrades.reduce((sum: number, item: PnLHistoryItem) => sum + item.realized_pnl, 0) / profitableTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ? losingTrades.reduce((sum: number, item: PnLHistoryItem) => sum + item.realized_pnl, 0) / losingTrades.length : 0;
    const profitFactor = Math.abs(avgLoss) > 0 ? avgWin / Math.abs(avgLoss) : 0;

    const maxPnL = Math.max(...filteredData.map((item: PnLHistoryItem) => item.realized_pnl));
    const minPnL = Math.min(...filteredData.map((item: PnLHistoryItem) => item.realized_pnl));

    return {
      totalPnL,
      winRate,
      avgWin,
      avgLoss,
      profitFactor,
      maxPnL,
      minPnL,
      totalTrades: filteredData.length,
      profitableTrades: profitableTrades.length,
      losingTrades: losingTrades.length
    };
  }, [filteredData]);

  // Use precise Chart.js typings for callbacks to avoid any
  // import type { ChartOptions, TooltipItem, ScriptableScaleContext } from 'chart.js';
  type SupportedChartTypes = 'line' | 'bar';
  type ScaleGridContext = ScriptableScaleContext & { tick?: { value?: number } };

  const chartOptions: ChartOptions<SupportedChartTypes> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: false,
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        callbacks: {
          label: (context: TooltipItem<SupportedChartTypes>): string | string[] => {
            const label = context.dataset.label ?? '';
            const value = typeof context.parsed?.y === 'number' ? context.parsed.y : 0;
            return `${label}: ${formatCurrency(value)}`;
          }
        }
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Time'
        }
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'P&L (USD)'
        },
        grid: {
          color: (context: ScaleGridContext): string => {
            const tickValue = context.tick?.value;
            if (typeof tickValue === 'number' && tickValue === 0) {
              return 'rgba(0, 0, 0, 0.3)';
            }
            return 'rgba(0, 0, 0, 0.1)';
          },
        },
      },
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false,
    },
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <div className={`card ${className}`}>
        <div className="card-header">
          <h3 className="card-title">Profit & Loss Analysis</h3>
        </div>
        <div className="card-content">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`card ${className}`}>
        <div className="card-header">
          <h3 className="card-title text-red-600">Profit & Loss Analysis - Error</h3>
        </div>
        <div className="card-content">
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">Failed to load P&L data</p>
            <button onClick={() => refetch()} className="btn btn-primary">
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`card ${className}`}>
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h3 className="card-title flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Profit & Loss Analysis
            {isRefetching && <InlineSpinner className="ml-2" />}
          </h3>
          <button
            onClick={() => refetch()}
            disabled={isRefetching}
            className="btn btn-secondary btn-sm"
          >
            <TrendingUp className={`w-4 h-4 ${isRefetching ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      <div className="card-content">
        {/* Controls */}
        <div className="mb-6 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Time Period Selector */}
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <select
                value={timePeriod}
                onChange={(e) => setTimePeriod(e.target.value as TimePeriod)}
                className="input input-sm"
              >
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
                <option value="all">All Time</option>
              </select>
            </div>

            {/* Chart Type Selector */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setChartType('line')}
                className={`btn btn-sm ${
                  chartType === 'line' ? 'btn-primary' : 'btn-secondary'
                }`}
              >
                <LineChart className="w-4 h-4" />
              </button>
              <button
                onClick={() => setChartType('bar')}
                className={`btn btn-sm ${
                  chartType === 'bar' ? 'btn-primary' : 'btn-secondary'
                }`}
              >
                <BarChart3 className="w-4 h-4" />
              </button>
            </div>

            {/* Cumulative Toggle */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showCumulative}
                onChange={(e) => setShowCumulative(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm">Show Cumulative</span>
            </label>
          </div>
        </div>

        {/* Statistics Cards */}
        {statistics && (
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6">
            <div className={`p-4 rounded-lg ${
              statistics.totalPnL >= 0 ? 'bg-green-50' : 'bg-red-50'
            }`}>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-gray-600">Total P&L</div>
                  <div className={`text-lg font-bold ${
                    statistics.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {formatCurrency(statistics.totalPnL)}
                  </div>
                </div>
                {statistics.totalPnL >= 0 ? (
                  <TrendingUp className="w-6 h-6 text-green-600" />
                ) : (
                  <TrendingDown className="w-6 h-6 text-red-600" />
                )}
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600">Win Rate</div>
              <div className="text-lg font-bold text-blue-600">
                {formatPercentage(statistics.winRate)}
              </div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600">Avg Win</div>
              <div className="text-lg font-bold text-green-600">
                {formatCurrency(statistics.avgWin)}
              </div>
            </div>

            <div className="bg-red-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600">Avg Loss</div>
              <div className="text-lg font-bold text-red-600">
                {formatCurrency(statistics.avgLoss)}
              </div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600">Profit Factor</div>
              <div className="text-lg font-bold text-purple-600">
                {statistics.profitFactor.toFixed(2)}
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600">Total Trades</div>
              <div className="text-lg font-bold text-gray-600">
                {statistics.totalTrades}
              </div>
            </div>
          </div>
        )}

        {/* Chart */}
        {chartData && filteredData.length > 0 ? (
          <div className="mb-6" style={{ height: `${height}px` }}>
            {chartType === 'line' ? (
              <Line data={chartData} options={chartOptions} />
            ) : (
              <Bar data={chartData} options={chartOptions} />
            )}
          </div>
        ) : (
          <div className="text-center py-12">
            <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-600 mb-2">No P&L data available</h4>
            <p className="text-gray-500">
              P&L data will appear here once you complete some trades
            </p>
          </div>
        )}

        {/* Asset Breakdown */}
        {(() => {
          // For now, let's show a simplified view since we need to access the raw WebSocket data
          const totalRealized = plData?.realized_pnl || 0;
          const totalUnrealized = plData?.unrealized_pnl || 0;
          const totalPnL = totalRealized + totalUnrealized;

          if (totalPnL === 0) return null;

          return (
            <div className="border-t pt-6 mb-6">
              <h4 className="text-lg font-semibold mb-4">Portfolio Summary</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 rounded-lg border bg-blue-50 border-blue-200">
                  <div className="text-sm text-gray-600 mb-1">Total Realized P&L</div>
                  <div className={`text-lg font-bold ${totalRealized >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatCurrency(totalRealized)}
                  </div>
                </div>
                <div className="p-4 rounded-lg border bg-blue-50 border-blue-200">
                  <div className="text-sm text-gray-600 mb-1">Total Unrealized P&L</div>
                  <div className={`text-lg font-bold ${totalUnrealized >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatCurrency(totalUnrealized)}
                  </div>
                </div>
                <div className="p-4 rounded-lg border bg-blue-50 border-blue-200">
                  <div className="text-sm text-gray-600 mb-1">Total Portfolio P&L</div>
                  <div className={`text-lg font-bold ${totalPnL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatCurrency(totalPnL)}
                  </div>
                </div>
              </div>
            </div>
          );
        })()}

        {/* Detailed Statistics */}
        {statistics && statistics.totalTrades > 0 && (
          <div className="border-t pt-6">
            <h4 className="text-lg font-semibold mb-4">Detailed Statistics</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div>
                <div className="text-sm text-gray-600">Profitable Trades</div>
                <div className="text-lg font-medium text-green-600">
                  {statistics.profitableTrades} ({formatPercentage((statistics.profitableTrades / statistics.totalTrades) * 100)})
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Losing Trades</div>
                <div className="text-lg font-medium text-red-600">
                  {statistics.losingTrades} ({formatPercentage((statistics.losingTrades / statistics.totalTrades) * 100)})
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Best Trade</div>
                <div className="text-lg font-medium text-green-600">
                  {formatCurrency(statistics.maxPnL)}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Worst Trade</div>
                <div className="text-lg font-medium text-red-600">
                  {formatCurrency(statistics.minPnL)}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Risk/Reward Ratio</div>
                <div className="text-lg font-medium text-blue-600">
                  1:{statistics.profitFactor.toFixed(2)}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Time Period</div>
                <div className="text-lg font-medium text-gray-600">
                  {timePeriod === 'all' ? 'All Time' : timePeriod.toUpperCase()}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfitLossChart;