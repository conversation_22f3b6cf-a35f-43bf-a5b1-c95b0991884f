/**
 * TradeHistoryCard Component
 * 
 * Displays comprehensive trade history with filtering, pagination, and real-time updates.
 * Integrates with the Flask backend API for trade data retrieval.
 */

import React, { useState, useMemo, useEffect } from 'react';
import { Clock, TrendingUp, TrendingDown, Filter, Download, RefreshCw, Calendar, BarChart3, AlertCircle } from 'lucide-react';
import { useTradeHistory } from '../../hooks/useApi';
import { LoadingSpinner, InlineSpinner } from '../ui/LoadingSpinner';
import type { TradeHistoryItem } from '../../types/api';

interface TradeHistoryCardProps {
  className?: string;
}

interface DateRange {
  start: string;
  end: string;
}

type FilterType = 'all' | 'buy' | 'sell';
type SortField = 'timestamp' | 'symbol' | 'quantity' | 'price' | 'total';
type SortDirection = 'asc' | 'desc';
type ViewMode = 'table' | 'cards';

const TradeHistoryCard: React.FC<TradeHistoryCardProps> = ({ className = '' }) => {
  const [filter, setFilter] = useState<FilterType>('all');
  const [sortField, setSortField] = useState<SortField>('timestamp');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('table');
  const [dateRange, setDateRange] = useState<DateRange>({ start: '', end: '' });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);

  const { data: trades, isLoading, error, refetch, isRefetching } = useTradeHistory();

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      refetch();
      setLastUpdateTime(new Date());
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, refetch]);

  // Enhanced filtering and sorting trades
  const filteredAndSortedTrades = useMemo(() => {
    if (!trades?.trades) return [];

    const filtered = trades.trades.filter((trade: TradeHistoryItem) => {
      const matchesFilter = filter === 'all' || trade.side.toLowerCase() === filter;
      const matchesSearch = searchTerm === '' ||
        trade.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trade.id.toString().includes(searchTerm);

      // Date range filtering
      const matchesDateRange = (
        (!dateRange.start || new Date(trade.timestamp) >= new Date(dateRange.start)) &&
        (!dateRange.end || new Date(trade.timestamp) <= new Date(dateRange.end))
      );

      return matchesFilter && matchesSearch && matchesDateRange;
    });

    const compare = <T extends number | string>(a: T, b: T): number => (a > b ? 1 : a < b ? -1 : 0);

    filtered.sort((a, b) => {
      switch (sortField) {
        case 'timestamp': {
          const aValue = new Date(a.timestamp).getTime();
          const bValue = new Date(b.timestamp).getTime();
          return sortDirection === 'asc' ? compare<number>(aValue, bValue) : compare<number>(bValue, aValue);
        }
        case 'symbol': {
          const aValue = a.symbol.toLowerCase();
          const bValue = b.symbol.toLowerCase();
          return sortDirection === 'asc' ? compare<string>(aValue, bValue) : compare<string>(bValue, aValue);
        }
        case 'quantity': {
          const aValue = Number(a.quantity);
          const bValue = Number(b.quantity);
          return sortDirection === 'asc' ? compare<number>(aValue, bValue) : compare<number>(bValue, aValue);
        }
        case 'price': {
          const aValue = Number(a.price);
          const bValue = Number(b.price);
          return sortDirection === 'asc' ? compare<number>(aValue, bValue) : compare<number>(bValue, aValue);
        }
        case 'total': {
          const aValue = Number(a.quantity) * Number(a.price);
          const bValue = Number(b.quantity) * Number(b.price);
          return sortDirection === 'asc' ? compare<number>(aValue, bValue) : compare<number>(bValue, aValue);
        }
        default:
          return 0;
      }
    });

    return filtered;
  }, [trades, filter, sortField, sortDirection, searchTerm, dateRange.start, dateRange.end]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedTrades.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedTrades = filteredAndSortedTrades.slice(startIndex, startIndex + itemsPerPage);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const clearFilters = () => {
    setFilter('all');
    setSearchTerm('');
    setDateRange({ start: '', end: '' });
    setCurrentPage(1);
  };

  const toggleAutoRefresh = () => {
    setAutoRefresh(!autoRefresh);
  };

  const formatCurrency = (value: string | number, symbol: string = 'USD') => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: symbol.includes('USD') ? 'USD' : 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 8
    }).format(num);
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const exportTrades = () => {
    if (!filteredAndSortedTrades.length) return;

    const csvContent = [
      ['Date', 'Symbol', 'Side', 'Quantity', 'Price', 'Total', 'Order ID'].join(','),
      ...filteredAndSortedTrades.map(trade => [
        new Date(trade.timestamp).toISOString(),
        trade.symbol,
        trade.side,
        trade.quantity,
        trade.price,
        (parseFloat(trade.quantity) * parseFloat(trade.price)).toFixed(8),
        trade.id
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `trade-history-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className={`card ${className}`}>
        <div className="card-header">
          <h3 className="card-title">Trade History</h3>
        </div>
        <div className="card-content">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`card ${className}`}>
        <div className="card-header">
          <h3 className="card-title text-red-600">Trade History - Error</h3>
        </div>
        <div className="card-content">
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">Failed to load trade history</p>
            <button onClick={() => refetch()} className="btn btn-primary">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`card ${className}`}>
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h3 className="card-title flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Trade History
            {isRefetching && <InlineSpinner className="ml-2" />}
          </h3>
          <div className="flex items-center space-x-2">
            {/* Auto-refresh toggle */}
            <button
              onClick={toggleAutoRefresh}
              className={`btn btn-sm ${autoRefresh ? 'btn-primary' : 'btn-secondary'}`}
              title={autoRefresh ? 'Auto-refresh enabled' : 'Auto-refresh disabled'}
            >
              <RefreshCw className={`w-4 h-4 ${autoRefresh && !isRefetching ? 'animate-pulse' : ''}`} />
            </button>

            {/* Manual refresh */}
            <button
              onClick={() => refetch()}
              disabled={isRefetching}
              className="btn btn-secondary btn-sm"
              title="Refresh now"
            >
              <RefreshCw className={`w-4 h-4 ${isRefetching ? 'animate-spin' : ''}`} />
            </button>

            {/* View mode toggle */}
            <div className="flex border border-gray-300 rounded-md overflow-hidden">
              <button
                onClick={() => setViewMode('table')}
                className={`btn btn-sm px-3 py-1 ${viewMode === 'table' ? 'btn-primary' : 'btn-secondary'}`}
                title="Table view"
              >
                <BarChart3 className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('cards')}
                className={`btn btn-sm px-3 py-1 ${viewMode === 'cards' ? 'btn-primary' : 'btn-secondary'}`}
                title="Card view"
              >
                <div className="grid grid-cols-2 gap-1 w-4 h-4">
                  <div className="w-1 h-1 bg-current rounded"></div>
                  <div className="w-1 h-1 bg-current rounded"></div>
                  <div className="w-1 h-1 bg-current rounded"></div>
                  <div className="w-1 h-1 bg-current rounded"></div>
                </div>
              </button>
            </div>

            {/* Export */}
            <button
              onClick={exportTrades}
              disabled={!filteredAndSortedTrades.length}
              className="btn btn-secondary btn-sm"
              title="Export to CSV"
            >
              <Download className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      <div className="card-content">
        {/* Filters and Search */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-600" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as FilterType)}
                className="input input-sm border border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md pl-3 pr-8 py-2 bg-white shadow-sm"
              >
                <option value="all">All Trades</option>
                <option value="buy">Buy Orders Only</option>
                <option value="sell">Sell Orders Only</option>
              </select>
              {filter !== 'all' && (
                <button
                  onClick={() => setFilter('all')}
                  className="text-xs text-gray-500 hover:text-gray-700 ml-1"
                >
                  Clear filter
                </button>
              )}
            </div>
            <div className="flex-1">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search by symbol or order ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input input-sm w-full border border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md pl-10 pr-4 py-2 bg-white shadow-sm"
                />
                {searchTerm && (
                  <button
                    onClick={() => setSearchTerm('')}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                )}
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>
            <button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className="flex items-center space-x-2 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-100 transition-colors"
            >
              <Calendar className="w-4 h-4" />
              <span>Advanced</span>
            </button>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date Range Start
                  </label>
                  <input
                    type="datetime-local"
                    value={dateRange.start}
                    onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                    className="input input-sm w-full border border-gray-300 rounded-md"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date Range End
                  </label>
                  <input
                    type="datetime-local"
                    value={dateRange.end}
                    onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                    className="input input-sm w-full border border-gray-300 rounded-md"
                  />
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={autoRefresh}
                      onChange={toggleAutoRefresh}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Auto-refresh (30s)</span>
                  </label>
                  {lastUpdateTime && (
                    <span className="text-xs text-gray-500">
                      Last update: {lastUpdateTime.toLocaleTimeString()}
                    </span>
                  )}
                </div>
                <button
                  onClick={clearFilters}
                  className="text-sm text-red-600 hover:text-red-800 flex items-center space-x-1"
                >
                  <AlertCircle className="w-4 h-4" />
                  <span>Clear all filters</span>
                </button>
              </div>
            </div>
          )}

          {/* Active filters indicator */}
          {(filter !== 'all' || searchTerm) && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span className="flex items-center">
                <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Active filters:
              </span>
              {filter !== 'all' && (
                <span className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-md">
                  {filter === 'buy' ? 'Buy orders' : 'Sell orders'}
                </span>
              )}
              {searchTerm && (
                <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-800 rounded-md font-mono">
                  Search: "{searchTerm}"
                </span>
              )}
              <button
                onClick={() => { setFilter('all'); setSearchTerm(''); }}
                className="text-blue-600 hover:text-blue-800 ml-2 font-medium"
              >
                Clear all
              </button>
            </div>
          )}
        </div>

        {/* Enhanced Trade Statistics */}
        {trades?.trades && trades.trades.length > 0 && (
          <div className="mb-8 space-y-6">
            {/* Main Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                <div className="text-sm font-medium text-gray-600 mb-1">Total Trades</div>
                <div className="text-2xl font-bold text-gray-800">{trades?.trades?.length || 0}</div>
                <div className="text-xs text-gray-500 mt-1">All time</div>
              </div>
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg border border-green-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                <div className="text-sm font-medium text-green-600 mb-1">Buy Orders</div>
                <div className="text-2xl font-bold text-green-800">
                  {trades?.trades?.filter((t: TradeHistoryItem) => t.side.toLowerCase() === 'buy').length || 0}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {trades?.trades?.length ?
                    `${((trades?.trades?.filter((t: TradeHistoryItem) => t.side.toLowerCase() === 'buy').length || 0) / trades.trades.length * 100).toFixed(1)}% of total`
                    : '0%'}
                </div>
              </div>
              <div className="bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-lg border border-red-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                <div className="text-sm font-medium text-red-600 mb-1">Sell Orders</div>
                <div className="text-2xl font-bold text-red-800">
                  {trades?.trades?.filter((t: TradeHistoryItem) => t.side.toLowerCase() === 'sell').length || 0}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {trades?.trades?.length ?
                    `${((trades?.trades?.filter((t: TradeHistoryItem) => t.side.toLowerCase() === 'sell').length || 0) / trades.trades.length * 100).toFixed(1)}% of total`
                    : '0%'}
                </div>
              </div>
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                <div className="text-sm font-medium text-blue-600 mb-1">Filtered</div>
                <div className="text-2xl font-bold text-blue-800">
                  {filteredAndSortedTrades.length}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {filteredAndSortedTrades.length === trades.trades.length ? 'All shown' : `${((filteredAndSortedTrades.length / trades.trades.length) * 100).toFixed(1)}% shown`}
                </div>
              </div>
            </div>

            {/* Additional Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200 shadow-sm">
                <div className="text-sm font-medium text-purple-600 mb-1">Total Volume</div>
                <div className="text-xl font-bold text-purple-800">
                  {formatCurrency(
                    filteredAndSortedTrades.reduce((sum, trade) => sum + parseFloat(trade.quantity) * parseFloat(trade.price), 0),
                    'USD'
                  )}
                </div>
              </div>
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-lg border border-orange-200 shadow-sm">
                <div className="text-sm font-medium text-orange-600 mb-1">Average Trade Size</div>
                <div className="text-xl font-bold text-orange-800">
                  {formatCurrency(
                    filteredAndSortedTrades.length > 0
                      ? filteredAndSortedTrades.reduce((sum, trade) => sum + parseFloat(trade.quantity) * parseFloat(trade.price), 0) / filteredAndSortedTrades.length
                      : 0,
                    'USD'
                  )}
                </div>
              </div>
              <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-4 rounded-lg border border-indigo-200 shadow-sm">
                <div className="text-sm font-medium text-indigo-600 mb-1">Trade Frequency</div>
                <div className="text-xl font-bold text-indigo-800">
                  {filteredAndSortedTrades.length > 1
                    ? `${((filteredAndSortedTrades.length / Math.max(1, ((new Date(filteredAndSortedTrades[0]?.timestamp).getTime() - new Date(filteredAndSortedTrades[filteredAndSortedTrades.length - 1]?.timestamp).getTime()) / (1000 * 60 * 60 * 24))))).toFixed(1)} trades/day`
                    : `${filteredAndSortedTrades.length} trade`
                  }
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Trade Display */}
        {paginatedTrades.length > 0 ? (
          <>
            {viewMode === 'table' ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-300 bg-gray-50">
                      <th
                        className="text-left py-3 px-3 cursor-pointer hover:bg-gray-100 transition-colors duration-150 flex items-center"
                        onClick={() => handleSort('timestamp')}
                      >
                        <span className="flex-1">Date</span>
                        {sortField === 'timestamp' && (
                          <span className="ml-1 text-gray-500">
                            {sortDirection === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </th>
                      <th
                        className="text-left py-3 px-3 cursor-pointer hover:bg-gray-100 transition-colors duration-150 flex items-center"
                        onClick={() => handleSort('symbol')}
                      >
                        <span className="flex-1">Symbol</span>
                        {sortField === 'symbol' && (
                          <span className="ml-1 text-gray-500">
                            {sortDirection === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </th>
                      <th className="text-left py-3 px-3">Side</th>
                      <th
                        className="text-right py-3 px-3 cursor-pointer hover:bg-gray-100 transition-colors duration-150 flex items-center justify-end"
                        onClick={() => handleSort('quantity')}
                      >
                        <span className="flex-1 text-right">Quantity</span>
                        {sortField === 'quantity' && (
                          <span className="ml-1 text-gray-500">
                            {sortDirection === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </th>
                      <th
                        className="text-right py-3 px-3 cursor-pointer hover:bg-gray-100 transition-colors duration-150 flex items-center justify-end"
                        onClick={() => handleSort('price')}
                      >
                        <span className="flex-1 text-right">Price</span>
                        {sortField === 'price' && (
                          <span className="ml-1 text-gray-500">
                            {sortDirection === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </th>
                      <th
                        className="text-right py-3 px-3 cursor-pointer hover:bg-gray-100 transition-colors duration-150 flex items-center justify-end"
                        onClick={() => handleSort('total')}
                      >
                        <span className="flex-1 text-right">Total</span>
                        {sortField === 'total' && (
                          <span className="ml-1 text-gray-500">
                            {sortDirection === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedTrades.map((trade: TradeHistoryItem, index: number) => {
                      const total = parseFloat(trade.quantity) * parseFloat(trade.price);
                      const isBuy = trade.side.toLowerCase() === 'buy';

                      return (
                        <tr key={`${trade.id}-${index}`} className="border-b border-gray-200 hover:bg-gray-50 transition-colors duration-150">
                          <td className="py-3 px-3 text-sm text-gray-700">
                            {formatDate(trade.timestamp)}
                          </td>
                          <td className="py-3 px-3 font-medium text-gray-800">
                            <span className="inline-flex items-center px-2 py-1 bg-gray-100 rounded-md text-xs font-mono">
                              {trade.symbol}
                            </span>
                          </td>
                          <td className="py-3 px-3">
                            <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                              isBuy ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'
                            }`}>
                              {isBuy ? (
                                <TrendingUp className="w-3 h-3 mr-1" />
                              ) : (
                                <TrendingDown className="w-3 h-3 mr-1" />
                              )}
                              {trade.side.toUpperCase()}
                            </span>
                          </td>
                          <td className="py-3 px-3 text-right font-mono text-sm text-gray-700">
                            {parseFloat(trade.quantity).toFixed(8)}
                          </td>
                          <td className="py-3 px-3 text-right font-mono text-sm text-gray-700">
                            {formatCurrency(trade.price)}
                          </td>
                          <td className="py-3 px-3 text-right font-mono text-sm font-medium text-gray-800">
                            {formatCurrency(total)}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            ) : (
              /* Card View */
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {paginatedTrades.map((trade: TradeHistoryItem, index: number) => {
                  const total = parseFloat(trade.quantity) * parseFloat(trade.price);
                  const isBuy = trade.side.toLowerCase() === 'buy';

                  return (
                    <div
                      key={`${trade.id}-${index}`}
                      className="p-4 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                            isBuy ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'
                          }`}>
                            {isBuy ? (
                              <TrendingUp className="w-3 h-3 mr-1" />
                            ) : (
                              <TrendingDown className="w-3 h-3 mr-1" />
                            )}
                            {trade.side.toUpperCase()}
                          </span>
                          <span className="inline-flex items-center px-2 py-1 bg-gray-100 rounded-md text-xs font-mono">
                            {trade.symbol}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatDate(trade.timestamp)}
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="text-gray-600 mb-1">Quantity</div>
                          <div className="font-mono font-medium text-gray-800">
                            {parseFloat(trade.quantity).toFixed(8)}
                          </div>
                        </div>
                        <div>
                          <div className="text-gray-600 mb-1">Price</div>
                          <div className="font-mono font-medium text-gray-800">
                            {formatCurrency(trade.price)}
                          </div>
                        </div>
                        <div className="col-span-2">
                          <div className="text-gray-600 mb-1">Total Value</div>
                          <div className={`font-mono font-medium ${isBuy ? 'text-green-800' : 'text-red-800'}`}>
                            {formatCurrency(total)}
                          </div>
                        </div>
                        <div className="col-span-2">
                          <div className="text-gray-600 mb-1">Order ID</div>
                          <div className="font-mono text-xs text-gray-700">
                            {trade.id}
                          </div>
                        </div>
                      </div>

                      {trade.status && trade.status !== 'FILLED' && (
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <div className="text-xs text-gray-600">Status</div>
                          <div className="text-sm font-medium text-orange-600">
                            {trade.status}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}

            {/* Enhanced Pagination */}
            {totalPages > 1 && (
              <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredAndSortedTrades.length)} of {filteredAndSortedTrades.length} trades</span>
                    <span className="mx-2">•</span>
                    <span>Page {currentPage} of {totalPages}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(1)}
                      disabled={currentPage === 1}
                      className="btn btn-secondary btn-sm px-3 py-1"
                    >
                      First
                    </button>
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="btn btn-secondary btn-sm px-3 py-1"
                    >
                      Previous
                    </button>
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                        return (
                          <button
                            key={pageNum}
                            onClick={() => setCurrentPage(pageNum)}
                            disabled={pageNum === currentPage}
                            className={`btn btn-sm px-3 py-1 ${
                              pageNum === currentPage
                                ? 'btn-primary'
                                : 'btn-secondary'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}
                    </div>
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="btn btn-secondary btn-sm px-3 py-1"
                    >
                      Next
                    </button>
                    <button
                      onClick={() => setCurrentPage(totalPages)}
                      disabled={currentPage === totalPages}
                      className="btn btn-secondary btn-sm px-3 py-1"
                    >
                      Last
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-600 mb-2">No trades found</h4>
            <p className="text-gray-500">
              {searchTerm || filter !== 'all' 
                ? 'Try adjusting your filters or search terms'
                : 'Trade history will appear here once you start trading'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TradeHistoryCard;