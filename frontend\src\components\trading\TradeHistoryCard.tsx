/**
 * TradeHistoryCard Component
 * 
 * Displays comprehensive trade history with filtering, pagination, and real-time updates.
 * Integrates with the Flask backend API for trade data retrieval.
 */

import React, { useState, useMemo } from 'react';
import { Clock, TrendingUp, TrendingDown, Filter, Download, RefreshCw } from 'lucide-react';
import { useTradeHistory } from '../../hooks/useApi';
import { LoadingSpinner, InlineSpinner } from '../ui/LoadingSpinner';
import type { TradeHistoryItem } from '../../types/api';

interface TradeHistoryCardProps {
  className?: string;
}

type FilterType = 'all' | 'buy' | 'sell';
type SortField = 'timestamp' | 'symbol' | 'quantity' | 'price' | 'total';
type SortDirection = 'asc' | 'desc';

const TradeHistoryCard: React.FC<TradeHistoryCardProps> = ({ className = '' }) => {
  const [filter, setFilter] = useState<FilterType>('all');
  const [sortField, setSortField] = useState<SortField>('timestamp');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');

  const { data: trades, isLoading, error, refetch, isRefetching } = useTradeHistory();

  // Filter and sort trades
  const filteredAndSortedTrades = useMemo(() => {
    if (!trades?.trades) return [];

    const filtered = trades.trades.filter((trade: TradeHistoryItem) => {
      const matchesFilter = filter === 'all' || trade.side.toLowerCase() === filter;
      const matchesSearch = searchTerm === '' || 
        trade.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trade.id.toString().includes(searchTerm);
      return matchesFilter && matchesSearch;
    });

    const compare = <T extends number | string>(a: T, b: T): number => (a > b ? 1 : a < b ? -1 : 0);

    filtered.sort((a, b) => {
      switch (sortField) {
        case 'timestamp': {
          const aValue = new Date(a.timestamp).getTime();
          const bValue = new Date(b.timestamp).getTime();
          return sortDirection === 'asc' ? compare<number>(aValue, bValue) : compare<number>(bValue, aValue);
        }
        case 'symbol': {
          const aValue = a.symbol.toLowerCase();
          const bValue = b.symbol.toLowerCase();
          return sortDirection === 'asc' ? compare<string>(aValue, bValue) : compare<string>(bValue, aValue);
        }
        case 'quantity': {
          const aValue = Number(a.quantity);
          const bValue = Number(b.quantity);
          return sortDirection === 'asc' ? compare<number>(aValue, bValue) : compare<number>(bValue, aValue);
        }
        case 'price': {
          const aValue = Number(a.price);
          const bValue = Number(b.price);
          return sortDirection === 'asc' ? compare<number>(aValue, bValue) : compare<number>(bValue, aValue);
        }
        case 'total': {
          const aValue = Number(a.quantity) * Number(a.price);
          const bValue = Number(b.quantity) * Number(b.price);
          return sortDirection === 'asc' ? compare<number>(aValue, bValue) : compare<number>(bValue, aValue);
        }
        default:
          return 0;
      }
    });

    return filtered;
  }, [trades, filter, sortField, sortDirection, searchTerm]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedTrades.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedTrades = filteredAndSortedTrades.slice(startIndex, startIndex + itemsPerPage);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const formatCurrency = (value: string | number, symbol: string = 'USD') => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: symbol.includes('USD') ? 'USD' : 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 8
    }).format(num);
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const exportTrades = () => {
    if (!filteredAndSortedTrades.length) return;

    const csvContent = [
      ['Date', 'Symbol', 'Side', 'Quantity', 'Price', 'Total', 'Order ID'].join(','),
      ...filteredAndSortedTrades.map(trade => [
        new Date(trade.timestamp).toISOString(),
        trade.symbol,
        trade.side,
        trade.quantity,
        trade.price,
        (parseFloat(trade.quantity) * parseFloat(trade.price)).toFixed(8),
        trade.id
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `trade-history-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className={`card ${className}`}>
        <div className="card-header">
          <h3 className="card-title">Trade History</h3>
        </div>
        <div className="card-content">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`card ${className}`}>
        <div className="card-header">
          <h3 className="card-title text-red-600">Trade History - Error</h3>
        </div>
        <div className="card-content">
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">Failed to load trade history</p>
            <button onClick={() => refetch()} className="btn btn-primary">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`card ${className}`}>
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h3 className="card-title flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Trade History
            {isRefetching && <InlineSpinner className="ml-2" />}
          </h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => refetch()}
              disabled={isRefetching}
              className="btn btn-secondary btn-sm"
            >
              <RefreshCw className={`w-4 h-4 ${isRefetching ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={exportTrades}
              disabled={!filteredAndSortedTrades.length}
              className="btn btn-secondary btn-sm"
            >
              <Download className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      <div className="card-content">
        {/* Filters and Search */}
        <div className="mb-6 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as FilterType)}
                className="input input-sm"
              >
                <option value="all">All Trades</option>
                <option value="buy">Buy Orders</option>
                <option value="sell">Sell Orders</option>
              </select>
            </div>
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search by symbol or order ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input input-sm w-full"
              />
            </div>
          </div>
        </div>

        {/* Trade Statistics */}
        {trades?.trades && trades.trades.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-sm text-gray-600">Total Trades</div>
              <div className="text-lg font-semibold">{trades?.trades?.length || 0}</div>
            </div>
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="text-sm text-gray-600">Buy Orders</div>
              <div className="text-lg font-semibold text-green-600">
                {trades?.trades?.filter((t: TradeHistoryItem) => t.side.toLowerCase() === 'buy').length || 0}
              </div>
            </div>
            <div className="bg-red-50 p-3 rounded-lg">
              <div className="text-sm text-gray-600">Sell Orders</div>
              <div className="text-lg font-semibold text-red-600">
                {trades?.trades?.filter((t: TradeHistoryItem) => t.side.toLowerCase() === 'sell').length || 0}
              </div>
            </div>
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-sm text-gray-600">Filtered</div>
              <div className="text-lg font-semibold text-blue-600">
                {filteredAndSortedTrades.length}
              </div>
            </div>
          </div>
        )}

        {/* Trade Table */}
        {paginatedTrades.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th 
                      className="text-left py-3 px-2 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('timestamp')}
                    >
                      Date {sortField === 'timestamp' && (sortDirection === 'asc' ? '↑' : '↓')}
                    </th>
                    <th 
                      className="text-left py-3 px-2 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('symbol')}
                    >
                      Symbol {sortField === 'symbol' && (sortDirection === 'asc' ? '↑' : '↓')}
                    </th>
                    <th className="text-left py-3 px-2">Side</th>
                    <th 
                      className="text-right py-3 px-2 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('quantity')}
                    >
                      Quantity {sortField === 'quantity' && (sortDirection === 'asc' ? '↑' : '↓')}
                    </th>
                    <th 
                      className="text-right py-3 px-2 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('price')}
                    >
                      Price {sortField === 'price' && (sortDirection === 'asc' ? '↑' : '↓')}
                    </th>
                    <th 
                      className="text-right py-3 px-2 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('total')}
                    >
                      Total {sortField === 'total' && (sortDirection === 'asc' ? '↑' : '↓')}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {paginatedTrades.map((trade: TradeHistoryItem, index: number) => {
                    const total = parseFloat(trade.quantity) * parseFloat(trade.price);
                    const isBuy = trade.side.toLowerCase() === 'buy';
                    
                    return (
                      <tr key={`${trade.id}-${index}`} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-2 text-sm">
                          {formatDate(trade.timestamp)}
                        </td>
                        <td className="py-3 px-2 font-medium">
                          {trade.symbol}
                        </td>
                        <td className="py-3 px-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            isBuy ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {isBuy ? (
                              <TrendingUp className="w-3 h-3 mr-1" />
                            ) : (
                              <TrendingDown className="w-3 h-3 mr-1" />
                            )}
                            {trade.side.toUpperCase()}
                          </span>
                        </td>
                        <td className="py-3 px-2 text-right font-mono text-sm">
                          {parseFloat(trade.quantity).toFixed(8)}
                        </td>
                        <td className="py-3 px-2 text-right font-mono text-sm">
                          {formatCurrency(trade.price)}
                        </td>
                        <td className="py-3 px-2 text-right font-mono text-sm font-medium">
                          {formatCurrency(total)}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-600">
                  Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredAndSortedTrades.length)} of {filteredAndSortedTrades.length} trades
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="btn btn-secondary btn-sm"
                  >
                    Previous
                  </button>
                  <span className="text-sm text-gray-600">
                    Page {currentPage} of {totalPages}
                  </span>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="btn btn-secondary btn-sm"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-600 mb-2">No trades found</h4>
            <p className="text-gray-500">
              {searchTerm || filter !== 'all' 
                ? 'Try adjusting your filters or search terms'
                : 'Trade history will appear here once you start trading'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TradeHistoryCard;