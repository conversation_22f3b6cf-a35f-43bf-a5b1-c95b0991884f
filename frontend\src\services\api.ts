/**
 * API Service Layer for TradingView Frontend
 * Handles secure communication with Flask backend
 */

import axios, { type AxiosInstance, type AxiosResponse, type AxiosError } from 'axios';
import type {
  AccountBalance,
  BalanceResponse,
  TradeResponse,
  ConfigResponse,
  SystemStatus,
  WebhookData,
  TradeHistoryResponse,
  PnLResponse,
  TradingConfig,
} from '../types/api';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
const API_TIMEOUT = 30000; // 30 seconds - increased timeout for rate limited responses
const MAX_RETRY_ATTEMPTS = 1; // Minimal retry attempts to prevent IP bans
const RETRY_DELAY_BASE = 10000; // 10 second base delay - increased to prevent IP bans
const CIRCUIT_BREAKER_THRESHOLD = 3; // Lower threshold to fail faster on rate limits
const CIRCUIT_BREAKER_TIMEOUT = 300000; // 5 minutes - much longer timeout for IP ban recovery

interface RetryConfig {
  attempts: number;
  delay: number;
  backoff: number;
}

interface CircuitBreakerState {
  failures: number;
  lastFailureTime: number;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
}

class ApiService {
  private client: AxiosInstance;
  private isOnline: boolean = true;
  private retryConfig: RetryConfig = {
    attempts: MAX_RETRY_ATTEMPTS,
    delay: RETRY_DELAY_BASE,
    backoff: 2,
  };
  private circuitBreaker: CircuitBreakerState = {
    failures: 0,
    lastFailureTime: 0,
    state: 'CLOSED',
  };
  private requestQueue: Map<string, Promise<unknown>> = new Map();
  private requestCache: Map<string, { data: unknown; timestamp: number }> = new Map();
  private lastRequestTime: number = 0;
  private readonly MIN_REQUEST_INTERVAL = 2000; // Minimum 2 seconds between requests to prevent IP bans
  private readonly CACHE_DURATION = 5000; // 5 seconds cache

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: API_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Check circuit breaker before making request
        if (!this.canMakeRequest()) {
          return Promise.reject(new Error('Circuit breaker is open. Service temporarily unavailable.'));
        }
        
        // Add timestamp to prevent caching
        config.params = {
          ...config.params,
          _t: Date.now(),
        };
        
        console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[API] Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        this.isOnline = true;
        this.recordSuccess();
        console.log(`[API] Response ${response.status}:`, response.data);
        return response;
      },
      (error: AxiosError) => {
        this.handleApiError(error);
        // recordFailure() is called within handleApiError() when appropriate
        return Promise.reject(error);
      }
    );
  }

  /**
   * Handle API errors with proper logging and user feedback
   */
  private handleApiError(error: AxiosError): void {
    // Don't record failure for canceled requests (React strict mode)
    if (error.message && error.message.includes('canceled')) {
      console.log('[API] Request canceled, not recording as failure');
      return;
    }
    
    if (error.code === 'ECONNABORTED') {
      console.error('[API] Request timeout');
      this.recordFailure();
    } else if (error.response) {
      // Server responded with error status
      const status = error.response.status;
      console.error(`[API] Server error ${status}:`, error.response.data);
      this.isOnline = true;
      
      // Handle different server errors appropriately
      if (status === 503) {
        // Service unavailable - don't record as failure, it's expected during rate limiting
        console.log('[API] Service temporarily unavailable (503) - not recording as failure');
        // Don't call recordSuccess() or recordFailure() for 503 - it's neutral
      } else if (status >= 500) {
        this.recordFailure();
      } else if (status >= 400) {
        // Client errors (4xx) shouldn't trigger circuit breaker
        console.log(`[API] Client error ${status} - not recording as failure`);
      } else {
        this.recordSuccess();
      }
    } else if (error.request) {
      // Network error
      console.error('[API] Network error:', error.message);
      this.isOnline = false;
      this.recordFailure();
    } else {
      console.error('[API] Unknown error:', error.message);
      this.recordFailure();
    }
  }

  /**
   * Record a successful API call for circuit breaker
   */
  private recordSuccess(): void {
    const previousState = this.circuitBreaker.state;
    this.circuitBreaker.failures = 0;
    this.circuitBreaker.state = 'CLOSED';
    
    if (previousState !== 'CLOSED') {
      console.log(`[API] Circuit breaker recovered: ${previousState} -> CLOSED`);
    }
  }

  /**
   * Check if cached data is available and valid
   */
  private getCachedData<T>(key: string): T | null {
    const cached = this.requestCache.get(key);
    if (cached && (Date.now() - cached.timestamp) < this.CACHE_DURATION) {
      console.log(`[API] Using cached data for ${key}`);
      return cached.data as T;
    }
    return null;
  }

  /**
   * Cache successful response data
   */
  private setCachedData<T>(key: string, data: T): void {
    this.requestCache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Record a failed API call for circuit breaker
   */
  private recordFailure(): void {
    this.circuitBreaker.failures++;
    this.circuitBreaker.lastFailureTime = Date.now();
    
    if (this.circuitBreaker.failures >= CIRCUIT_BREAKER_THRESHOLD) {
      this.circuitBreaker.state = 'OPEN';
      console.warn('[API] Circuit breaker opened due to repeated failures');
    }
  }

  /**
   * Check if circuit breaker allows requests
   */
  private canMakeRequest(): boolean {
    const now = Date.now();
    
    switch (this.circuitBreaker.state) {
      case 'CLOSED':
        return true;
      case 'OPEN': {
        if (now - this.circuitBreaker.lastFailureTime > CIRCUIT_BREAKER_TIMEOUT) {
          this.circuitBreaker.state = 'HALF_OPEN';
          console.log('[API] Circuit breaker transitioning to half-open - testing service availability');
          return true;
        }
        const timeRemaining = Math.round((CIRCUIT_BREAKER_TIMEOUT - (now - this.circuitBreaker.lastFailureTime)) / 1000);
        console.warn(`[API] Circuit breaker is OPEN - ${timeRemaining}s remaining until retry`);
        return false;
      }
      case 'HALF_OPEN':
        console.log('[API] Circuit breaker is HALF_OPEN - testing request');
        return true;
      default:
        return true;
    }
  }

  /**
   * Get current local circuit breaker status for monitoring
   */
  public getLocalCircuitBreakerStatus() {
    const now = Date.now();
    return {
      state: this.circuitBreaker.state,
      failures: this.circuitBreaker.failures,
      threshold: CIRCUIT_BREAKER_THRESHOLD,
      lastFailureTime: this.circuitBreaker.lastFailureTime,
      timeUntilRetry: this.circuitBreaker.state === 'OPEN' 
        ? Math.max(0, CIRCUIT_BREAKER_TIMEOUT - (now - this.circuitBreaker.lastFailureTime))
        : 0,
      isOnline: this.isOnline
    };
  }

  /**
   * Throttle requests to prevent overwhelming the server
   */
  private async throttleRequest(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.MIN_REQUEST_INTERVAL) {
      const delay = this.MIN_REQUEST_INTERVAL - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    this.lastRequestTime = Date.now();
  }

  /**
   * Execute request with retry logic and circuit breaker
   */
  private async executeWithRetry<T>(
    requestFn: () => Promise<T>,
    context: string = 'API Request'
  ): Promise<T> {
    // Check cache first
    const cachedData = this.getCachedData<T>(context);
    if (cachedData) {
      return cachedData;
    }

    // Check if there's already a pending request for this context
    if (this.requestQueue.has(context)) {
      console.log(`[API] Reusing pending request for ${context}`);
      return this.requestQueue.get(context)! as Promise<T>;
    }

    if (!this.canMakeRequest()) {
      // Try to return cached data even if expired during circuit breaker
      const expiredCache = this.requestCache.get(context);
      if (expiredCache) {
        console.log(`[API] Using expired cache for ${context} due to circuit breaker`);
        return expiredCache.data as T;
      }
      
      const timeRemaining = Math.max(0, CIRCUIT_BREAKER_TIMEOUT - (Date.now() - this.circuitBreaker.lastFailureTime));
      const secondsRemaining = Math.ceil(timeRemaining / 1000);
      throw new Error(`Circuit breaker is open. Service temporarily unavailable. Retry in ${secondsRemaining} seconds.`);
    }

    // Throttle the request
    await this.throttleRequest();

    const requestPromise = this.executeRequest(requestFn, context);
    this.requestQueue.set(context, requestPromise);
    
    try {
      const result = await requestPromise;
      this.setCachedData<T>(context, result);
      return result;
    } finally {
      this.requestQueue.delete(context);
    }
  }

  /**
   * Execute the actual request with retry logic
   */
  private async executeRequest<T>(requestFn: () => Promise<T>, context: string): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= this.retryConfig.attempts; attempt++) {
      try {
        const result = await requestFn();
        this.recordSuccess();
        return result;
      } catch (error) {
        lastError = error as Error;
        
        // Don't record failure for canceled requests (React strict mode)
        if (error instanceof Error && error.message.includes('canceled')) {
          console.log(`[API] Request canceled for ${context}, not recording as failure`);
          throw error;
        }
        
        // Don't retry on client errors (4xx) except 408, 429
        if (axios.isAxiosError(error) && error.response) {
          const status = error.response.status;
          
          // Handle 503 Service Unavailable with special logic for IP bans
          if (status === 503) {
            console.warn(`[API] Service unavailable (503) for ${context}, attempt ${attempt}/${this.retryConfig.attempts}`);
            this.recordFailure();
            
            // Check if this is an IP ban response
            const responseData = error.response.data;
            if (responseData && typeof responseData === 'object' && 
                ('ban_info' in responseData || 
                 (typeof responseData.error === 'string' && responseData.error.includes('IP banned')))) {
              console.error('[API] IP ban detected - stopping all retries');
              // Force circuit breaker open for longer period
              this.circuitBreaker.failures = CIRCUIT_BREAKER_THRESHOLD + 5;
              this.circuitBreaker.lastFailureTime = Date.now();
              this.circuitBreaker.state = 'OPEN';
              throw error; // Don't retry on IP bans
            }
            
            // If this is the last attempt, don't retry
            if (attempt >= this.retryConfig.attempts) {
              throw error;
            }
            
            // Use much longer exponential backoff for 503 errors to prevent IP bans
            const baseDelay = Math.min(30000, this.retryConfig.delay * Math.pow(3, attempt - 1)); // 3x backoff, max 30s
            const jitter = Math.random() * 5000; // Add up to 5 seconds jitter
            const totalDelay = baseDelay + jitter;
            
            console.log(`[API] Waiting ${Math.round(totalDelay)}ms before retry ${attempt + 1} (extended backoff for 503)`);
            await new Promise(resolve => setTimeout(resolve, totalDelay));
            continue;
          }
          
          if (status >= 400 && status < 500 && status !== 408 && status !== 429) {
            // Record failure for server errors that shouldn't be retried
            if (status >= 500) {
              this.recordFailure();
            }
            throw error;
          }
        }
        
        if (attempt === this.retryConfig.attempts) {
          console.error(`[API] ${context} failed after ${attempt} attempts:`, error);
          // Record failure when all retry attempts are exhausted
          this.recordFailure();
          break;
        }
        
        // Exponential backoff with jitter to prevent thundering herd and IP bans
        const baseDelay = this.retryConfig.delay * Math.pow(this.retryConfig.backoff, attempt - 1);
        const jitter = Math.random() * 0.5 * baseDelay; // Add up to 50% jitter for better distribution
        const delay = Math.min(baseDelay + jitter, 60000); // Cap at 60 seconds for IP ban recovery
        console.warn(`[API] ${context} attempt ${attempt} failed, retrying in ${Math.round(delay)}ms:`, error);
        
        // Record failure for retry attempts
        this.recordFailure();
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }

  /**
   * Get system status and health check
   */
  async getSystemStatus(options?: { signal?: AbortSignal }): Promise<SystemStatus> {
    return this.executeWithRetry(
      () => this.client.get<SystemStatus>('/api/health', {
        signal: options?.signal,
      }).then(response => response.data),
      'System Status'
    );
  }

  /**
   * Get circuit breaker status from backend
   */
  async getCircuitBreakerStatus(options?: { signal?: AbortSignal }) {
    try {
      const response = await this.client.get('/api/circuit-breaker-status', {
        signal: options?.signal,
        timeout: 5000 // Short timeout for status checks
      });
      return response.data;
    } catch (error) {
      console.warn('[API] Failed to get circuit breaker status:', error);
      return {
        service_available: false,
        health_score: 0,
        recommendation: 'use_websocket'
      };
    }
  }

  /**
   * Get account balances for all assets
   */
  async getAccountBalances(options?: { signal?: AbortSignal }): Promise<AccountBalance[]> {
    return this.executeWithRetry(
      async () => {
        const response = await this.client.get<BalanceResponse>('/balance', { signal: options?.signal });
        
        if (response.data.status === 'error') {
          throw new Error(response.data.message || 'Failed to get balances');
        }
        
        return response.data.balances || [];
      },
      'Account Balances'
    );
  }

  /**
   * Get trading bot configuration
   */
  async getTradingConfig(options?: { signal?: AbortSignal }): Promise<TradingConfig> {
    return this.executeWithRetry(
      () => this.client.get<TradingConfig>('/config', {
        signal: options?.signal,
      }).then(response => response.data),
      'Trading Config'
    );
  }

  /**
   * Execute a manual trade (for testing)
   */
  async executeTrade(tradeData: WebhookData, options?: { signal?: AbortSignal }): Promise<TradeResponse> {
    return this.executeWithRetry(
      () => this.client.post<TradeResponse>('/test', tradeData, {
        signal: options?.signal,
      }).then(response => response.data),
      'Execute Trade'
    );
  }

  /**
   * Toggle emergency stop
   */
  async toggleEmergencyStop(options?: { signal?: AbortSignal }): Promise<ConfigResponse> {
    try {
      const response = await this.client.post<ConfigResponse>('/emergency-stop', undefined, { signal: options?.signal });
      return response.data;
    } catch (error) {
      throw this.createApiError('Failed to toggle emergency stop', error);
    }
  }

  /**
   * Send debug webhook data
   */
  async sendDebugWebhook(data: Record<string, string>, options?: { signal?: AbortSignal }): Promise<unknown> {
    try {
      const response = await this.client.post('/debug', data, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        signal: options?.signal,
      });
      return response.data as unknown;
    } catch (error) {
      throw this.createApiError('Failed to send debug webhook', error);
    }
  }

  /**
   * Get trade history (backend implementation required)
   */
  async getTradeHistory(page: number = 1, limit: number = 50, options?: { signal?: AbortSignal }): Promise<TradeHistoryResponse> {
    return this.executeWithRetry(
      () => this.client.get<TradeHistoryResponse>('/trades', {
        params: { page, limit },
        signal: options?.signal,
      }).then(response => response.data),
      'Trade History'
    );
  }

  /**
   * Get profit/loss data (backend implementation required)
   */
  async getPnLData(options?: { signal?: AbortSignal }): Promise<PnLResponse> {
    return this.executeWithRetry(
      () => this.client.get<PnLResponse>('/pnl', {
        signal: options?.signal,
      }).then(response => response.data),
      'P&L Data'
    );
  }

  /**
   * Check if API is online
   */
  isApiOnline(): boolean {
    return this.isOnline;
  }

  /**
   * Create standardized Error enriched with Axios metadata
   */
  private createApiError(message: string, originalError: unknown): Error & { code?: string; status?: number; details?: unknown } {
    const err = new Error(message) as Error & { code?: string; status?: number; details?: unknown };

    if (axios.isAxiosError(originalError)) {
      err.status = originalError.response?.status;
      // Prefer backend-provided code/message when available
      const backendCode = (originalError.response?.data as { code?: string } | undefined)?.code;
      if (backendCode) err.code = String(backendCode);
      err.details = originalError.response?.data ?? (originalError as AxiosError).toJSON?.() ?? originalError.message;
    } else if (originalError instanceof Error) {
      err.details = { message: originalError.message };
    } else {
      err.details = originalError as unknown;
    }

    return err;
  }

  /**
   * Get current market prices for symbols
   */
  async getPrices(symbols?: string[], options?: { signal?: AbortSignal }): Promise<{ prices: Record<string, number>; timestamp: string; count: number }> {
    const params = symbols ? { symbols: symbols.join(',') } : {};
    const response = await this.client.get<{ prices: Record<string, number>; timestamp: string; count: number }>(
      '/prices',
      { params, signal: options?.signal }
    );
    return response.data;
  }

  /**
   * Test API connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.client.get('/api/health', { timeout: 5000 });
      this.isOnline = true;
      this.recordSuccess();
      return true;
    } catch {
      this.isOnline = false;
      this.recordFailure();
      return false;
    }
  }

  /**
   * Get API service status including circuit breaker state
   */
  getServiceStatus() {
    return {
      isOnline: this.isOnline,
      circuitBreaker: {
        state: this.circuitBreaker.state,
        failures: this.circuitBreaker.failures,
        lastFailureTime: this.circuitBreaker.lastFailureTime,
        canMakeRequest: this.canMakeRequest(),
      },
      retryConfig: this.retryConfig,
    };
  }

  /**
   * Reset circuit breaker (for manual recovery)
   */
  resetCircuitBreaker(): void {
    this.circuitBreaker.failures = 0;
    this.circuitBreaker.state = 'CLOSED';
    this.circuitBreaker.lastFailureTime = 0;
    console.log('[API] Circuit breaker manually reset');
  }
}

// Export singleton instance
export const apiService = new ApiService();
export const api = apiService; // Alias for compatibility
export default apiService;