# PnL Calculation Issues Analysis

## Critical Issues Identified

### 1. **Commission Handling Error** ❌
**Problem**: Commission is being subtracted from realized PnL for EVERY inventory lot sold, even when selling partial quantities.

**Current Code Issue**:
```python
# This subtracts commission for each inventory lot, not per trade
realized_pnl += inv_qty * (price - inv_price) - commission
```

**Impact**: 
- In Test 6: Expected $0.00 realized PnL, but got $5.00
- Commission should be allocated proportionally or handled per trade, not per inventory lot

### 2. **Unrealized PnL Calculation Discrepancy** ❌
**Problem**: In Test 3, expected $22.50 but got $27.50 unrealized PnL.

**Analysis**:
- Remaining inventory: 0.5 @ $100 + 1.0 @ $110
- Average cost basis: (0.5 × $100 + 1.0 × $110) / 1.5 = $106.67
- Unrealized PnL: 1.5 × ($125 - $106.67) = $27.50 ✓ (Actually correct!)
- The test expectation was wrong, not the calculation

### 3. **Commission Allocation Strategy** ⚠️
**Problem**: Current implementation doesn't follow standard accounting practices for commission allocation.

**Industry Standard**: <mcreference link="https://www.kucoin.com/support/**************" index="2">2</mcreference> <mcreference link="https://cointelegraph.com/news/what-is-profit-and-loss-pnl-and-how-to-calculate-it" index="4">4</mcreference>
- Buy commissions should increase cost basis
- Sell commissions should reduce realized gains
- Commission should be allocated proportionally when selling partial positions

## Recommended Fixes

### 1. **Fix Commission Handling**
```python
def calculate_fifo_pnl_fixed(trades, current_quantity, current_price):
    # Track commissions separately for buy and sell trades
    # Allocate sell commission proportionally to the quantity sold
```

### 2. **Implement Proper Cost Basis Calculation**
According to FIFO methodology: <mcreference link="https://mobee.com/en/mobee-academy/blog/profit-and-loss" index="5">5</mcreference>
- Buy orders: Add quantity and adjust cost basis including commission
- Sell orders: Remove from oldest inventory first, allocate commission proportionally

### 3. **Add Validation Checks**
- Ensure inventory quantities match current holdings
- Validate that all sells have corresponding buys
- Check for negative inventory scenarios

## Formula Verification

### Realized PnL Formula: <mcreference link="https://help.crypto.com/en/articles/3529029-how-is-profit-loss-p-l-calculated" index="1">1</mcreference>
```
Realized PnL = (Exit Price - Entry Price) × Quantity Sold - Trading Fees
```

### Unrealized PnL Formula: <mcreference link="https://www.kucoin.com/support/**************" index="2">2</mcreference>
```
Unrealized PnL = (Current Price - Average Cost Basis) × Current Quantity
```

### FIFO Cost Basis: <mcreference link="https://cointelegraph.com/news/what-is-profit-and-loss-pnl-and-how-to-calculate-it" index="4">4</mcreference>
```
Average Cost Basis = Total Cost of Remaining Inventory / Total Remaining Quantity
```

## Test Results Summary

| Test | Status | Issue |
|------|--------|-------|
| Test 1: Buy & Hold | ✅ PASS | - |
| Test 2: Buy Low, Sell High | ⚠️ Minor | Commission calculation off by $0.10 |
| Test 3: Multiple Buys, Partial Sell | ⚠️ Test Error | Expected value was wrong, calculation correct |
| Test 4: Complex Scenario | ✅ PASS | - |
| Test 5: No Trades | ✅ PASS | - |
| Test 6: High Commission | ❌ FAIL | Commission handling error |

## Priority Actions

1. **HIGH**: Fix commission allocation in FIFO calculation
2. **MEDIUM**: Add input validation and error handling
3. **LOW**: Optimize performance for large trade histories
4. **LOW**: Add support for different cost basis methods (LIFO, Average Cost)