/**
 * Real-time Price Data Hook
 * Provides live market prices for portfolio calculations and trading interface
 */

import { useQuery } from '@tanstack/react-query';
import { apiService } from '../services/api';
import { auditLogger } from '../utils/auditLogger';

export interface PriceData {
  prices: Record<string, number>;
  timestamp: string;
  count: number;
}

/**
 * Hook to fetch current market prices for specified symbols
 */
export function usePrices(symbols?: string[], options?: {
  refetchInterval?: number;
  enabled?: boolean;
}) {
  const { refetchInterval = 30000, enabled = true } = options || {};

  const query = useQuery({
    queryKey: ['prices', symbols?.sort().join(',') || 'default'],
    queryFn: async ({ signal }): Promise<PriceData> => {
      const data = await apiService.getPrices(symbols, { signal });
      
      // Log successful price data fetch
      auditLogger.log({
        action: 'FETCH_LIVE_PRICES',
        component: 'usePrices',
        dataSource: 'live',
        description: `Successfully fetched live prices for ${data.count} symbols`,
        changes: [
          `Retrieved prices for symbols: ${Object.keys(data.prices).join(', ')}`,
          `Data timestamp: ${data.timestamp}`,
        ],
        verification: {
          status: 'verified',
          details: `Live price data successfully retrieved from Binance API at ${data.timestamp}`,
        },
        metadata: {
          symbolCount: data.count,
          symbols: Object.keys(data.prices),
          priceRange: {
            min: Math.min(...Object.values(data.prices)),
            max: Math.max(...Object.values(data.prices)),
          },
        },
      });
      
      return data;
    },
    refetchInterval,
    enabled,
    staleTime: 25000, // Consider data stale after 25 seconds
    gcTime: 60000, // Keep in cache for 1 minute
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  return {
    prices: query.data?.prices || {},
    timestamp: query.data?.timestamp,
    count: query.data?.count || 0,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

/**
 * Hook to get price for a specific symbol
 */
export function useSymbolPrice(symbol: string) {
  const { prices, isLoading, isError, error, timestamp } = usePrices([symbol]);
  
  return {
    price: prices[symbol],
    isLoading,
    isError,
    error,
    timestamp,
  };
}