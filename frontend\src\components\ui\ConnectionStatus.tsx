/**
 * Connection Status Component
 * Displays real-time connection status to the backend API
 */

import { Wifi, WifiOff, AlertCircle, Activity } from 'lucide-react';
import { useSystemStatus, useApiConnectivity } from '../../hooks/useApi';
import { useRealtimeData } from '../../hooks/useWebSocket';

interface ConnectionStatusProps {
  className?: string;
}

/**
 * Shows connection status with visual indicators and retry functionality
 */
function ConnectionStatus({ className = '' }: ConnectionStatusProps) {
  const { data: systemStatus, isLoading, error, refetch } = useSystemStatus();
  const { isConnected: apiConnected } = useApiConnectivity();
  const realtimeData = useRealtimeData();

  const getConnectionState = () => {
    if (isLoading) {
      return {
        status: 'connecting' as const,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100',
        icon: AlertCircle,
        message: 'Connecting...',
      };
    }

    if (error || !systemStatus) {
      return {
        status: 'disconnected' as const,
        color: 'text-red-600',
        bgColor: 'bg-red-100',
        icon: WifiOff,
        message: 'API Disconnected',
      };
    }

    if (realtimeData.webSocket.connecting) {
      return {
        status: 'connecting' as const,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100',
        icon: Activity,
        message: 'WebSocket Connecting...',
      };
    }

    if (!realtimeData.webSocket.connected) {
      return {
        status: 'partial' as const,
        color: 'text-orange-600',
        bgColor: 'bg-orange-100',
        icon: AlertCircle,
        message: 'API Connected, WebSocket Disconnected',
      };
    }

    return {
      status: 'connected' as const,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      icon: Wifi,
      message: 'Fully Connected',
    };
  };

  const connectionState = getConnectionState();
  const Icon = connectionState.icon;

  const handleRetry = () => {
    refetch();
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
        connectionState.bgColor
      } ${connectionState.color}`}>
        <Icon className="h-4 w-4" />
        <span className="font-medium">{connectionState.message}</span>
      </div>
      
      {connectionState.status === 'disconnected' && (
        <button
          onClick={handleRetry}
          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          title="Retry connection"
        >
          Retry
        </button>
      )}
      
      {/* Connection details tooltip */}
      <div className="relative group">
        <button className="text-gray-400 hover:text-gray-600">
          <AlertCircle className="h-4 w-4" />
        </button>
        
        <div className="absolute right-0 top-full mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">API Status:</span>
              <span className={apiConnected ? 'text-green-600' : 'text-red-600'}>
                {apiConnected ? 'Online' : 'Offline'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">WebSocket:</span>
              <span className={
                realtimeData.webSocket.connected 
                  ? 'text-green-600' 
                  : realtimeData.webSocket.connecting 
                  ? 'text-yellow-600' 
                  : 'text-red-600'
              }>
                {
                  realtimeData.webSocket.connected 
                    ? 'Connected' 
                    : realtimeData.webSocket.connecting 
                    ? 'Connecting...' 
                    : 'Disconnected'
                }
              </span>
            </div>
            
            {realtimeData.lastActivity > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Last Activity:</span>
                <span className="text-gray-900 dark:text-white">
                  {new Date(realtimeData.lastActivity).toLocaleTimeString()}
                </span>
              </div>
            )}
            
            {systemStatus?.last_heartbeat && (
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Last Update:</span>
                <span className="text-gray-900 dark:text-white">
                  {new Date(systemStatus.last_heartbeat).toLocaleTimeString()}
                </span>
              </div>
            )}
            
            {error && (
              <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                <p className="text-red-600 text-xs">
                  Error: {error.message || 'Connection failed'}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ConnectionStatus;