import os
import json
import logging
import asyncio
from datetime import datetime, timezone
from decimal import Decimal, ROUND_DOWN, InvalidOperation
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
from binance.client import Client
from binance.exceptions import Binance<PERSON>IEx<PERSON>, BinanceRequestException, BinanceOrderException
from binance import ThreadedWebsocketManager, BinanceSocketManager
from dotenv import load_dotenv
import time
import threading
from functools import wraps
from collections import defaultdict
import hmac
import hashlib
from typing import Dict, Any, Optional, List, DefaultDict, Union
from pydantic import BaseModel, ValidationError, validator, Field, field_validator
import re

# Load environment variables
load_dotenv()

# Pydantic validation models
class WebhookData(BaseModel):
    """Validation model for webhook data"""
    action: str = Field(..., pattern=r'^(BUY|SELL)$', description="Trading action")
    symbol: str = Field(..., min_length=3, max_length=20, description="Trading symbol")
    coin: Optional[str] = Field(None, min_length=1, max_length=10, description="Base coin")
    quantity: Optional[float] = Field(None, gt=0, description="Trade quantity")
    price: Optional[float] = Field(None, gt=0, description="Trade price")
    market_order: Optional[int] = Field(None, ge=0, le=1, description="Market order flag")
    
    @field_validator('symbol')
    @classmethod
    def validate_symbol(cls, v):
        if not re.match(r'^[A-Z]{3,20}$', v.upper()):
            raise ValueError('Symbol must contain only uppercase letters, 3-20 characters')
        return v.upper()
    
    @field_validator('action')
    @classmethod
    def validate_action(cls, v):
        return v.upper()

class ErrorLogData(BaseModel):
    """Validation model for frontend error logging"""
    timestamp: str = Field(..., description="Error timestamp")
    level: str = Field(..., pattern=r'^(error|warning|info)$', description="Log level")
    source: str = Field(..., pattern=r'^(frontend|backend)$', description="Error source")
    context: Dict[str, Any] = Field(..., description="Error context")
    userAgent: Optional[str] = Field(None, max_length=500, description="User agent")
    url: Optional[str] = Field(None, max_length=2000, description="Page URL")

class TradeRequest(BaseModel):
    """Validation model for trade execution requests"""
    action: str = Field(..., pattern=r'^(BUY|SELL)$', description="Trading action")
    symbol: str = Field(..., min_length=3, max_length=20, description="Trading symbol")
    coin: str = Field(..., min_length=1, max_length=10, description="Base coin")
    quantity: str = Field(..., description="Trade quantity as string")
    
    @field_validator('symbol')
    @classmethod
    def validate_symbol(cls, v):
        if not re.match(r'^[A-Z]{3,20}$', v.upper()):
            raise ValueError('Symbol must contain only uppercase letters, 3-20 characters')
        return v.upper()
    
    @field_validator('quantity')
    @classmethod
    def validate_quantity(cls, v):
        try:
            qty = float(v)
            if qty <= 0:
                raise ValueError('Quantity must be positive')
            if qty > 1000000:  # Reasonable upper limit
                raise ValueError('Quantity too large')
            return v
        except (ValueError, TypeError):
            raise ValueError('Quantity must be a valid positive number')

def clear_logs():
    """Clear log files to start fresh"""
    log_files = [
        'app.log',  # If we're using file logging
        'trading_bot.log'  # If we're using file logging
    ]
    
    for log_file in log_files:
        try:
            if os.path.exists(log_file):
                with open(log_file, 'w') as f:
                    f.write(f"=== Log cleared at {datetime.now(timezone.utc).isoformat()} ===\n")
                print(f"✅ Cleared log file: {log_file}")
        except Exception as e:
            print(f"Warning: Could not clear log file {log_file}: {e}")

# Clear logs at startup
print("Clearing previous logs...")
clear_logs()

# Initialize Flask app
app = Flask(__name__, static_folder='frontend/dist', static_url_path='')
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'default-secret-key')

# Track application startup time for uptime calculation
APP_START_TIME = datetime.now(timezone.utc)

# Enable CORS for all routes
CORS(app, cors_allowed_origins="*")
socketio = SocketIO(app, 
                   cors_allowed_origins="*", 
                   async_mode='eventlet',
                   logger=True,
                   engineio_logger=True,
                   ping_interval=25,
                   ping_timeout=60,
                   max_http_buffer_size=1000000,
                   allow_upgrades=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialization will be done after class definitions

# Enhanced WebSocket Manager for Binance Streams
class BinanceWebSocketManager:
    """Manages Binance WebSocket connections using latest python-binance features"""
    def __init__(self, api_key=None, api_secret=None, tld='us'):
        self.api_key = api_key
        self.api_secret = api_secret
        self.tld = tld
        self.twm = None  # ThreadedWebsocketManager
        self.bsm = None  # BinanceSocketManager (async)
        self.price_data = {}
        self.account_data = {}
        self.is_connected = False
        self.active_streams = {}
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        
    def initialize(self):
        """Initialize WebSocket client with latest features"""
        try:
            # Use ThreadedWebsocketManager for better stability
            self.twm = ThreadedWebsocketManager(
                api_key=self.api_key,
                api_secret=self.api_secret,
                tld=self.tld
            )
            logger.info("Enhanced WebSocket client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize WebSocket client: {e}")
            
    def connect(self):
        """Establish WebSocket connection"""
        try:
            if self.twm and not self.twm.is_alive():
                self.twm.start()
                self.is_connected = True
                logger.info("Enhanced WebSocket connection established")
        except Exception as e:
            logger.error(f"Failed to establish WebSocket connection: {e}")
            self.is_connected = False
            
    def subscribe_to_price_stream(self, symbol):
        """Subscribe to price updates for a symbol using optimized streams"""
        try:
            if not self.twm or not self.is_connected:
                logger.warning("WebSocket not connected, attempting to reconnect...")
                self.reconnect()
                
            if symbol not in self.active_streams and self.twm:
                # Use miniTicker for lighter weight (1000ms intervals)
                # Note: Pyright doesn't recognize this method but it exists in Binance library
                stream_key = self.twm.start_mini_ticker_socket(  # type: ignore[attr-defined]
                    callback=self._handle_price_message,
                    symbol=symbol
                )
                self.active_streams[symbol] = stream_key
                logger.info(f"Subscribed to miniTicker stream for {symbol}")
        except Exception as e:
            logger.error(f"Failed to subscribe to price stream for {symbol}: {e}")
            
    def subscribe_to_multiplex_streams(self, streams):
        """Subscribe to multiple streams efficiently using multiplexing"""
        try:
            if not self.twm or not self.is_connected:
                self.reconnect()
                
            # Format streams for multiplexing
            formatted_streams = [f"{stream.lower()}@miniTicker" for stream in streams]
            
            if self.twm:
                stream_key = self.twm.start_multiplex_socket(
                    callback=self._handle_multiplex_message,
                    streams=formatted_streams
                )
                
                for stream in streams:
                    self.active_streams[stream] = stream_key
                    
                logger.info(f"Subscribed to multiplex streams: {streams}")
            else:
                logger.warning("WebSocket manager not available for multiplex streaming")
        except Exception as e:
            logger.error(f"Failed to subscribe to multiplex streams: {e}")
            
    def _handle_price_message(self, msg):
        """Handle individual price messages with error handling"""
        try:
            if isinstance(msg, dict) and msg.get('e') == 'error':
                logger.error(f"WebSocket error: {msg}")
                self._handle_connection_error(msg.get('type'))
                return
                
            # Handle miniTicker data
            if 's' in msg and 'c' in msg:  # Symbol and close price
                symbol = msg['s']
                self.price_data[symbol] = {
                    'price': float(msg['c']),
                    'timestamp': msg.get('E', int(time.time() * 1000))
                }
                # Emit to frontend via SocketIO
                socketio.emit('price_update', {
                    'symbol': symbol,
                    'price': float(msg['c']),
                    'timestamp': msg.get('E', int(time.time() * 1000))
                })
        except Exception as e:
            logger.error(f"Error handling price message: {e}")
            
    def _handle_multiplex_message(self, msg):
        """Handle multiplexed messages efficiently"""
        try:
            if isinstance(msg, dict) and msg.get('e') == 'error':
                logger.error(f"Multiplex WebSocket error: {msg}")
                self._handle_connection_error(msg.get('type'))
                return
                
            # Handle multiplexed data
            if 'stream' in msg and 'data' in msg:
                stream_data = msg['data']
                if 's' in stream_data and 'c' in stream_data:
                    symbol = stream_data['s']
                    self.price_data[symbol] = {
                        'price': float(stream_data['c']),
                        'timestamp': stream_data.get('E', int(time.time() * 1000))
                    }
                    socketio.emit('price_update', {
                        'symbol': symbol,
                        'price': float(stream_data['c']),
                        'timestamp': stream_data.get('E', int(time.time() * 1000))
                    })
        except Exception as e:
            logger.error(f"Error handling multiplex message: {e}")
            
    def _handle_connection_error(self, error_type):
        """Handle connection errors with auto-reconnect"""
        logger.error(f"Connection error: {error_type}")
        self.is_connected = False
        
        if error_type == 'BinanceWebsocketClosed':
            # Auto-reconnect for closed connections
            self.reconnect_attempts += 1
            if self.reconnect_attempts <= self.max_reconnect_attempts:
                logger.info(f"Attempting to reconnect (attempt {self.reconnect_attempts})")
                time.sleep(2 ** self.reconnect_attempts)  # Exponential backoff
                self.reconnect()
            else:
                logger.error("Max reconnection attempts reached")
                
    def reconnect(self):
        """Reconnect WebSocket with cleanup"""
        try:
            if self.twm:
                self.twm.stop()
                
            self.active_streams.clear()
            self.reconnect_attempts = 0
            self.initialize()
            self.connect()
            
            # Resubscribe to streams
            if self.price_data:
                symbols = list(self.price_data.keys())
                self.subscribe_to_multiplex_streams(symbols)
                
        except Exception as e:
            logger.error(f"Reconnection failed: {e}")
            
    def get_cached_price(self, symbol):
        """Get cached price data to avoid REST API calls"""
        return self.price_data.get(symbol, {}).get('price')
        
    def get_all_prices(self):
        """Get all cached prices"""
        return self.price_data.copy()
        
    def disconnect(self):
        """Disconnect all streams and close connection"""
        try:
            if self.twm:
                self.twm.stop()
            self.active_streams.clear()
            self.is_connected = False
            logger.info("Enhanced WebSocket connection closed")
        except Exception as e:
            logger.error(f"Error disconnecting WebSocket: {e}")

# Enhanced Rate limiting and caching system
class RateLimiter:
    """Enhanced rate limiter with latest Binance US rate limits"""
    def __init__(self):
        self.request_times: DefaultDict[str, List[float]] = defaultdict(list)
        self.request_weights: DefaultDict[str, List[tuple[int, float]]] = defaultdict(list)
        self.cache: Dict[str, Any] = {}
        self.cache_ttl: Dict[str, float] = {}
        self.banned_until: Optional[float] = None
        self.used_weight: int = 0
        self.weight_reset_time: float = time.time()
        
        # Conservative Binance US rate limits to prevent IP bans
        self.rate_limits = {
            'request_weight': {'limit': 800, 'window': 60},      # Reduced from 1200 to 800
            'raw_requests': {'limit': 40000, 'window': 300},    # Reduced from 61k to 40k
            'orders': {'limit': 5000, 'window': 86400},         # Reduced from 10k to 5k
            'order_rate': {'limit': 5, 'window': 1},             # Reduced from 10 to 5
            'websocket': {'limit': 200, 'window': 300}           # Reduced from 300 to 200
        }
        
    def is_rate_limited(self, endpoint: str, weight: int = 1, limit_type: str = 'request_weight') -> bool:
        """Check if we're rate limited with request weight consideration"""
        now = time.time()
        
        # Check if we're currently banned
        if self.banned_until and now < self.banned_until:
            logger.warning(f"API is banned until {datetime.fromtimestamp(self.banned_until)}")
            return True
            
        # Get the appropriate rate limit
        rate_limit = self.rate_limits.get(limit_type, self.rate_limits['request_weight'])
        window = rate_limit['window']
        limit = rate_limit['limit']
        
        # Clean old requests
        self.request_times[endpoint] = [
            req_time for req_time in self.request_times[endpoint] 
            if now - req_time < window
        ]
        
        # Clean old weight records
        if limit_type == 'request_weight':
            self.request_weights[endpoint] = [
                (weight_val, req_time) for weight_val, req_time in self.request_weights[endpoint]
                if now - req_time < window
            ]
            
            # Calculate current weight usage
            current_weight = sum(w for w, _ in self.request_weights[endpoint])
            
            # Check if we're approaching the weight limit (90% for better utilization)
            if current_weight >= limit * 0.90:
                logger.warning(f"Approaching weight limit for {endpoint}: {current_weight}/{limit}")
                return True
        else:
            # For other rate limits (90% threshold for better utilization)
            if len(self.request_times[endpoint]) >= limit * 0.90:
                logger.warning(f"Approaching rate limit for {endpoint}: {len(self.request_times[endpoint])}/{limit}")
                return True
            
        return False
    
    def record_request(self, endpoint: str, weight: int = 1):
        """Record a request with its weight for rate limiting"""
        now = time.time()
        self.request_times[endpoint].append(now)
        
        if weight > 0:
            self.request_weights[endpoint].append((weight, now))
            self.used_weight += weight
            
        # Reset weight counter every minute
        if now - self.weight_reset_time > 60:
            self.used_weight = 0
            self.weight_reset_time = now
    
    def handle_rate_limit_error(self, error, retry_after: int = 0):
        """Handle rate limit errors with proper ban timing"""
        if retry_after:
            self.banned_until = time.time() + retry_after
            logger.error(f"Rate limited. Banned until {datetime.fromtimestamp(self.banned_until)}")
        else:
            # Exponential backoff for repeated offenses
            if self.banned_until:
                ban_duration = min(time.time() - self.banned_until + 300, 3600)  # Max 1 hour
            else:
                ban_duration = 300  # 5 minutes default
                
            self.banned_until = time.time() + ban_duration
            logger.error(f"Rate limited. Banned for {ban_duration/60:.1f} minutes until {datetime.fromtimestamp(self.banned_until)}")
    
    def get_cached(self, key: str) -> Optional[Any]:
        """Get cached data if still valid"""
        if key in self.cache and key in self.cache_ttl:
            if time.time() < self.cache_ttl[key]:
                return self.cache[key]
            else:
                # Clean expired cache
                del self.cache[key]
                del self.cache_ttl[key]
        return None
    
    def set_cache(self, key: str, value: Any, ttl: int = 30):
        """Set cached data with TTL"""
        self.cache[key] = value
        self.cache_ttl[key] = time.time() + ttl
        
    def get_rate_limit_status(self):
        """Get current rate limit status"""
        now = time.time()
        status = {}
        
        for limit_type, config in self.rate_limits.items():
            window = config['window']
            limit = config['limit']
            
            # Count recent requests
            if limit_type == 'request_weight':
                recent_weight = sum(w for w, req_time in self.request_weights.get('all', []) 
                                  if now - req_time < window)
                status[limit_type] = {
                    'current': recent_weight,
                    'limit': limit,
                    'usage_percent': (recent_weight / limit) * 100 if limit > 0 else 0
                }
            else:
                recent_requests = len([req_time for req_time in self.request_times.get(limit_type, []) 
                                      if now - req_time < window])
                status[limit_type] = {
                    'current': recent_requests,
                    'limit': limit,
                    'usage_percent': (recent_requests / limit) * 100 if limit > 0 else 0
                }
                
        return status

# Global rate limiter instance
rate_limiter = RateLimiter()

def with_rate_limit(endpoint: str, cache_key: str = "", cache_ttl: int = 30, weight: int = 1, max_retries: int = 3):
    """Enhanced decorator for rate limiting and caching API calls with intelligent retry"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retry_count = 0
            last_error = None
            
            while retry_count <= max_retries:
                try:
                    # Check cache first
                    if cache_key:
                        cached_result = rate_limiter.get_cached(cache_key)
                        if cached_result is not None:
                            logger.info(f"Returning cached result for {cache_key}")
                            return cached_result
                    
                    # Check rate limit with weight consideration
                    if rate_limiter.is_rate_limited(endpoint, weight=weight):
                        logger.warning(f"Rate limited for endpoint {endpoint} (weight: {weight})")
                        if cache_key:
                            # Return stale cache if available
                            stale_cache = rate_limiter.cache.get(cache_key)
                            if stale_cache:
                                logger.info(f"Returning stale cache for {cache_key}")
                                return stale_cache
                        
                        # Get retry information
                        rate_status = rate_limiter.get_rate_limit_status()
                        logger.error(f"Rate limit status: {rate_status}")
                        
                        if retry_count < max_retries:
                            # Exponential backoff
                            backoff_time = min(2 ** retry_count, 60)  # Max 60 seconds
                            logger.info(f"Rate limited, waiting {backoff_time}s before retry {retry_count + 1}/{max_retries}")
                            time.sleep(backoff_time)
                            retry_count += 1
                            continue
                        else:
                            raise Exception(f"Rate limited for {endpoint} after {max_retries} retries")
                    
                    # Record the request with weight
                    rate_limiter.record_request(endpoint, weight=weight)
                    
                    # Make the actual API call
                    result = func(*args, **kwargs)
                    
                    # Cache the result on success
                    if cache_key:
                        rate_limiter.set_cache(cache_key, result, cache_ttl)
                    
                    return result
                    
                except BinanceAPIException as e:
                    last_error = e
                    
                    if e.code == -1003:  # Too many requests / IP banned
                        retry_after = None
                        if hasattr(e, 'response') and e.response:
                            retry_after = e.response.headers.get('Retry-After')
                            if retry_after:
                                retry_after = int(retry_after)
                        
                        # Extract ban time from error message if available
                        if hasattr(e, 'message') and 'until' in e.message:
                            try:
                                import re
                                ban_time_match = re.search(r'until (\d+)', e.message)
                                if ban_time_match:
                                    ban_timestamp = int(ban_time_match.group(1))
                                    if ban_timestamp > time.time() * 1000:  # Milliseconds
                                        retry_after = (ban_timestamp - int(time.time() * 1000)) // 1000
                            except:
                                pass
                        
                        rate_limiter.handle_rate_limit_error(e, retry_after or 0)
                        
                        # If banned, don't retry - it's hopeless
                        if rate_limiter.banned_until and time.time() < rate_limiter.banned_until:
                            logger.error(f"IP banned until {datetime.fromtimestamp(rate_limiter.banned_until)} - not retrying")
                            raise
                        
                        if retry_count < max_retries:
                            # Exponential backoff for rate limits
                            backoff_time = min(2 ** retry_count, 300)  # Max 5 minutes
                            logger.info(f"Rate limited, waiting {backoff_time}s before retry {retry_count + 1}/{max_retries}")
                            time.sleep(backoff_time)
                            retry_count += 1
                            continue
                        else:
                            logger.error(f"Failed after {max_retries} retries: {e}")
                            raise
                    
                    elif e.code == -1021:  # Timestamp outside recv window
                        # This is usually not recoverable
                        logger.error(f"Timestamp error - not retryable: {e}")
                        raise
                    
                    elif e.code == -1005:  # No API key provided
                        # Configuration error - not retryable
                        logger.error(f"API key error - not retryable: {e}")
                        raise
                    
                    else:
                        # Other API errors might be retryable
                        if retry_count < max_retries:
                            logger.warning(f"API error {e.code}, retrying {retry_count + 1}/{max_retries}")
                            time.sleep(2 ** retry_count)  # Exponential backoff
                            retry_count += 1
                            continue
                        else:
                            logger.error(f"Failed after {max_retries} retries: {e}")
                            raise
                
                except Exception as e:
                    last_error = e
                    logger.error(f"Unexpected error in {endpoint}: {e}")
                    
                    if retry_count < max_retries:
                        logger.info(f"Retrying {retry_count + 1}/{max_retries} after error: {e}")
                        time.sleep(2 ** retry_count)
                        retry_count += 1
                        continue
                    else:
                        raise
            
            # If we get here, all retries failed
            if last_error:
                logger.error(f"All retries failed for {endpoint}: {last_error}")
                raise last_error
                
        return wrapper
    return decorator

class TradingBot:
    def __init__(self, binance_client):
        self.client = binance_client
        self.rate_limiter = rate_limiter  # Use global rate limiter
        self.ws_manager = ws_manager  # Use global WebSocket manager
        
        # Initialize default values first
        self.min_order_sizes = {
            'BTCUSD': 0.00001,
            'BTCUSDT': 0.00001,
            'ETHUSD': 0.001,
            'ETHUSDT': 0.001,
        }
        
        # Default fixed quantities (fallback)
        self.fixed_quantities = {
            'BTCUSDT': 0.001,  # Default will be overridden by .env
            'ETHUSDT': 0.01,   # Default will be overridden by .env
            'BTCUSD': 0.001,   # Default will be overridden by .env
            'ETHUSD': 0.01,    # Default will be overridden by .env
        }
        
        # Maximum quantity limits
        self.max_quantities = {
            'BTCUSDT': 0.1,
            'ETHUSDT': 1.0,
            'BTCUSD': 0.1,
            'ETHUSD': 1.0,
        }
        
        # Valid trading symbols to prevent invalid API calls
        self.valid_symbols = {
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
            'BTCUSD', 'ETHUSD'
        }
        
        # Load configuration from environment
        self.load_configuration()
        
    def initialize_websockets(self):
        """Initialize WebSocket connections for price streaming"""
        try:
            if self.ws_manager:
                self.ws_manager.initialize()
                self.ws_manager.connect()
                
                # Subscribe to price streams for allowed symbols using multiplexing
                allowed_symbols = os.getenv('ALLOWED_SYMBOLS', 'BTCUSDT,ETHUSDT').split(',')
                allowed_symbols = [s.strip() for s in allowed_symbols if s.strip()]
                
                if len(allowed_symbols) > 1:
                    # Use multiplexing for multiple symbols (more efficient)
                    self.ws_manager.subscribe_to_multiplex_streams(allowed_symbols)
                else:
                    # Use individual streams for single symbol
                    for symbol in allowed_symbols:
                        self.ws_manager.subscribe_to_price_stream(symbol)
                        
                logger.info(f"WebSocket streams initialized for symbols: {allowed_symbols}")
            else:
                logger.warning("WebSocket manager not available")
        except Exception as e:
            logger.error(f"Failed to initialize WebSocket streams: {e}")
    
    def load_configuration(self):
        """Load configuration from environment variables"""
        import os
        
        # Emergency stop
        self.emergency_stop = os.getenv('EMERGENCY_STOP', 'false').lower() == 'true'
        logger.info(f"Emergency stop: {self.emergency_stop}")
        
        # Allowed symbols
        allowed_symbols_str = os.getenv('ALLOWED_SYMBOLS', 'BTCUSDT,ETHUSDT,ETHUSD')
        self.allowed_symbols = [s.strip().upper() for s in allowed_symbols_str.split(',')]
        logger.info(f"Allowed symbols: {self.allowed_symbols}")
        
        # Fixed quantities (minimum viable amounts)
        self.fixed_quantities['BTCUSDT'] = float(os.getenv('FIXED_QUANTITY_BTCUSDT', '0.001'))
        self.fixed_quantities['ETHUSDT'] = float(os.getenv('FIXED_QUANTITY_ETHUSDT', '0.01'))
        self.fixed_quantities['BTCUSD'] = float(os.getenv('FIXED_QUANTITY_BTCUSD', '0.001'))
        self.fixed_quantities['ETHUSD'] = float(os.getenv('FIXED_QUANTITY_ETHUSD', '0.01'))
        
        logger.info(f"Fixed quantities: BTCUSDT={self.fixed_quantities['BTCUSDT']}, ETHUSDT={self.fixed_quantities['ETHUSDT']}")
        
        # Percentage configuration
        self.quantity_percentage = float(os.getenv('QUANTITY_PERCENTAGE', 5))
        logger.info(f"Quantity percentage: {self.quantity_percentage}%")
        
        # Stop Loss and Take Profit configuration (support N/A, none, or 0 to disable)
        stop_loss_env = os.getenv('STOP_LOSS_PERCENTAGE', '2.0').lower().strip()
        take_profit_env = os.getenv('TAKE_PROFIT_PERCENTAGE', '3.0').lower().strip()
        
        # Parse stop loss - support N/A, none, or 0 to disable
        if stop_loss_env in ['n/a', 'na', 'none', '0', '0.0', 'disabled', 'disable']:
            self.stop_loss_percentage = None
            logger.info("Stop loss: Disabled")
        else:
            try:
                self.stop_loss_percentage = float(stop_loss_env)
                logger.info(f"Stop loss: {self.stop_loss_percentage}%")
            except ValueError:
                logger.warning(f"Invalid STOP_LOSS_PERCENTAGE value '{stop_loss_env}', using default 2.0%")
                self.stop_loss_percentage = 2.0
        
        # Parse take profit - support N/A, none, or 0 to disable
        if take_profit_env in ['n/a', 'na', 'none', '0', '0.0', 'disabled', 'disable']:
            self.take_profit_percentage = None
            logger.info("Take profit: Disabled")
        else:
            try:
                self.take_profit_percentage = float(take_profit_env)
                logger.info(f"Take profit: {self.take_profit_percentage}%")
            except ValueError:
                logger.warning(f"Invalid TAKE_PROFIT_PERCENTAGE value '{take_profit_env}', using default 3.0%")
                self.take_profit_percentage = 3.0
        
        # Maximum quantities
        self.max_quantities['BTCUSDT'] = float(os.getenv('MAX_QUANTITY_BTCUSDT', '0.1'))
        self.max_quantities['ETHUSDT'] = float(os.getenv('MAX_QUANTITY_ETHUSDT', '1.0'))
        self.max_quantities['BTCUSD'] = float(os.getenv('MAX_QUANTITY_BTCUSD', '0.1'))
        self.max_quantities['ETHUSD'] = float(os.getenv('MAX_QUANTITY_ETHUSD', '1.0'))
        
        logger.info(f"Max quantities: BTCUSDT={self.max_quantities['BTCUSDT']}, ETHUSDT={self.max_quantities['ETHUSDT']}")
    
    def validate_symbol(self, symbol):
        """Validate symbol against allowed and valid symbols list"""
        symbol = symbol.upper()
        
        # Check if symbol is in the valid trading symbols list
        if symbol not in self.valid_symbols:
            logger.error(f"Invalid symbol {symbol}. Valid symbols are: {self.valid_symbols}")
            return False
            
        # Check if symbol is in allowed symbols list
        if symbol not in self.allowed_symbols:
            logger.error(f"Symbol {symbol} not in allowed list: {self.allowed_symbols}")
            return False
        
        return True
    
    def check_emergency_stop(self):
        """Check if emergency stop is activated"""
        if self.emergency_stop:
            logger.error("Emergency stop is activated - trading disabled")
            return False
        return True
    
    def get_max_quantity(self, symbol):
        """Get maximum allowed quantity for a symbol"""
        return self.max_quantities.get(symbol, 0.01)  # Default 0.01 for other symbols
    
    def validate_quantity(self, quantity, symbol):
        """Validate quantity against maximum limits"""
        max_qty = self.get_max_quantity(symbol)
        if quantity > max_qty:
            logger.error(f"Quantity {quantity} exceeds maximum {max_qty} for {symbol}")
            return False
        return True
    
    def calculate_percentage_quantity(self, symbol, side):
        """Calculate quantity based on percentage of available balance"""
        try:
            logger.info(f"Calculating percentage quantity for {symbol} {side}")
            
            if side.upper() == 'BUY':
                # For buy orders, use quote asset (USDT/USD)
                if 'USDT' in symbol:
                    quote_asset = 'USDT'
                elif 'USD' in symbol:
                    quote_asset = 'USD'
                else:
                    quote_asset = 'USDT'  # Default to USDT
                
                balance = self.get_account_balance(quote_asset)
                usable_balance = balance * (self.quantity_percentage / 100)
                
                # Get current price
                ticker = self.client.get_symbol_ticker(symbol=symbol)
                current_price = float(ticker['price'])
                
                quantity = usable_balance / current_price
                logger.info(f"Buy calculation: balance={balance}, usable={usable_balance}, price={current_price}, quantity={quantity}")
                
            else:  # SELL
                # For sell orders, use base asset (BTC/ETH)
                base_asset = symbol.replace('USDT', '').replace('USD', '')
                balance = self.get_account_balance(base_asset)
                quantity = balance * (self.quantity_percentage / 100)
                logger.info(f"Sell calculation: balance={balance}, quantity={quantity}")
            
            # Apply maximum quantity limits
            max_qty = self.get_max_quantity(symbol)
            quantity = min(quantity, max_qty)
            
            # Apply minimum quantity and precision
            min_qty = self.min_order_sizes.get(symbol, 0.00001)
            quantity = max(quantity, min_qty)
            
            # Apply precision formatting
            quantity = self.apply_precision_rules(quantity, symbol)
            
            # Validate final quantity
            if not self.validate_quantity(quantity, symbol):
                logger.error(f"Quantity validation failed for {symbol}")
                return 0.0
            
            logger.info(f"Final percentage quantity: {quantity}")
            return quantity
            
        except Exception as e:
            logger.error(f"Error calculating percentage quantity: {e}")
            return 0.0
    
    def apply_precision_rules(self, quantity, symbol):
        """Apply precision rules and step size formatting"""
        try:
            # Get symbol precision from exchange info
            symbol_info = self.client.get_symbol_info(symbol)
            if symbol_info:
                # Find the LOT_SIZE filter
                for filter in symbol_info.get('filters', []):
                    if filter.get('filterType') == 'LOT_SIZE':
                        step_size = filter.get('stepSize', '0.000001')
                        print(f"DEBUG: Step size for {symbol}: {step_size}")
                        
                        # Calculate precision from step size
                        if '.' in step_size:
                            precision = len(step_size.split('.')[1])
                        else:
                            precision = 0
                        print(f"DEBUG: Precision: {precision}")
                        
                        # Round to step size first
                        step_size_float = float(step_size)
                        quantity = round(quantity / step_size_float) * step_size_float
                        print(f"DEBUG: Quantity after step size: {quantity}")
                        
                        # Format with proper precision
                        quantity_str = "{:.{}f}".format(quantity, precision)
                        quantity = float(quantity_str)
                        print(f"DEBUG: Final quantity: {quantity}")
                        
                        # Ensure no scientific notation in the final float conversion
                        if quantity < 0.00001:
                            quantity = float("{:.8f}".format(quantity).rstrip('0').rstrip('.') or '0.00001')
                        break
            
            return quantity
            
        except Exception as e:
            print(f"DEBUG: Error in precision calculation: {e}")
            # Fallback to default precision
            if symbol.startswith('BTC'):
                quantity = round(quantity, 6)
            else:
                quantity = round(quantity, 4)
            return quantity
        
    @with_rate_limit('account', cache_key='account_balance', cache_ttl=120, weight=5)
    def get_account_balance(self, asset):
        """Get available balance for specific asset"""
        try:
            if self.client is None:
                logger.error("Binance client not initialized")
                return 0.0
            account = self.client.get_account()
            for balance in account['balances']:
                if balance['asset'] == asset:
                    return float(balance['free'])
            return 0.0
        except Exception as e:
            logger.error(f"Error getting balance for {asset}: {e}")
            return 0.0
    
    def calculate_quantity(self, symbol, side, percentage=90):
        """Calculate order quantity based on available balance"""
        try:
            # Input validation
            if not symbol or not isinstance(symbol, str):
                raise ValueError(f"Invalid symbol: {symbol}")
            if not side or not isinstance(side, str):
                raise ValueError(f"Invalid side: {side}")
            if not isinstance(percentage, (int, float)) or percentage <= 0 or percentage > 100:
                raise ValueError(f"Invalid percentage: {percentage}. Must be between 0 and 100")
            
            symbol = symbol.upper().strip()
            side = side.upper().strip()
            
            if side not in ['BUY', 'SELL']:
                raise ValueError(f"Invalid side: {side}. Must be 'BUY' or 'SELL'")
            
            if side == 'BUY':
                # For buy orders, use quote asset (USDT/USD)
                if 'USDT' in symbol:
                    quote_asset = 'USDT'
                elif 'USD' in symbol:
                    quote_asset = 'USD'
                else:
                    # Default fallback for other quote assets
                    quote_asset = 'USDT'
                    logger.warning(f"Unknown quote asset for {symbol}, defaulting to USDT")
                
                balance = self.get_account_balance(quote_asset)
                if balance <= 0:
                    raise ValueError(f"Insufficient {quote_asset} balance: {balance}")
                
                # Use percentage of available balance
                usable_balance = balance * (percentage / 100)
                if usable_balance <= 0:
                    raise ValueError(f"Usable balance too low: {usable_balance}")
                
                # Get current price with error handling
                try:
                    ticker = self.client.get_symbol_ticker(symbol=symbol)
                    if not ticker or 'price' not in ticker:
                        raise ValueError(f"Invalid ticker response for {symbol}")
                    current_price = float(ticker['price'])
                    if current_price <= 0:
                        raise ValueError(f"Invalid price for {symbol}: {current_price}")
                except Exception as price_error:
                    logger.error(f"Failed to get price for {symbol}: {price_error}")
                    raise ValueError(f"Could not retrieve price for {symbol}: {price_error}")
                
                # Calculate quantity
                quantity = usable_balance / current_price
                
            else:  # SELL
                # For sell orders, use base asset (BTC/ETH)
                base_asset = symbol.replace('USDT', '').replace('USD', '')
                if not base_asset or base_asset == symbol:
                    raise ValueError(f"Could not determine base asset for {symbol}")
                
                balance = self.get_account_balance(base_asset)
                if balance <= 0:
                    raise ValueError(f"Insufficient {base_asset} balance: {balance}")
                
                quantity = balance * (percentage / 100)
                if quantity <= 0:
                    raise ValueError(f"Calculated quantity too low: {quantity}")
            
            # Validate calculated quantity
            if not isinstance(quantity, (int, float)) or quantity <= 0:
                raise ValueError(f"Invalid calculated quantity: {quantity}")
            
            # Round down to avoid insufficient balance errors
            min_qty = self.min_order_sizes.get(symbol, 0.00001)
            if min_qty <= 0:
                min_qty = 0.00001
                logger.warning(f"Invalid min_qty for {symbol}, using default: {min_qty}")
            
            quantity = max(quantity, min_qty)
            
            # Round to appropriate decimal places with error handling
            try:
                if symbol.startswith('BTC'):
                    quantity = float(Decimal(str(quantity)).quantize(Decimal('0.00001'), rounding=ROUND_DOWN))
                else:
                    quantity = float(Decimal(str(quantity)).quantize(Decimal('0.001'), rounding=ROUND_DOWN))
            except (InvalidOperation, ValueError) as decimal_error:
                logger.error(f"Decimal quantization error for {symbol}: {decimal_error}")
                # Fallback to simple rounding
                if symbol.startswith('BTC'):
                    quantity = round(quantity, 5)
                else:
                    quantity = round(quantity, 3)
            
            # Ensure quantity is properly formatted for Binance API
            # Get symbol precision from exchange info
            try:
                symbol_info = self.client.get_symbol_info(symbol)
                if symbol_info:
                    # Find the LOT_SIZE filter
                    for filter in symbol_info.get('filters', []):
                        if filter.get('filterType') == 'LOT_SIZE':
                            step_size = filter.get('stepSize', '0.000001')
                            print(f"DEBUG: Step size for {symbol}: {step_size}")
                            
                            # Calculate precision from step size
                            if '.' in step_size:
                                precision = len(step_size.split('.')[1])
                            else:
                                precision = 0
                            print(f"DEBUG: Precision: {precision}")
                            
                            # Round to step size first
                            step_size_float = float(step_size)
                            quantity = round(quantity / step_size_float) * step_size_float
                            print(f"DEBUG: Quantity after step size: {quantity}")
                            
                            # Format with proper precision
                            quantity_str = "{:0.{}f}".format(quantity, precision)
                            quantity = float(quantity_str)
                            print(f"DEBUG: Final quantity: {quantity}")
                            
                            # Ensure no scientific notation in the final float conversion
                            if quantity < 0.00001:
                                quantity = float("{:.8f}".format(quantity).rstrip('0').rstrip('.') or '0.00001')
                            break
            except Exception as e:
                print(f"DEBUG: Error in precision calculation: {e}")
                # Fallback to default precision
                if symbol.startswith('BTC'):
                    quantity = round(quantity, 6)
                else:
                    quantity = round(quantity, 4)
                
            return quantity
            
        except Exception as e:
            logger.error(f"Error calculating quantity: {e}")
            return 0.0
    
    @with_rate_limit('order', cache_key='order_market', cache_ttl=0, weight=1)
    def place_market_order(self, symbol, side, custom_quantity=None, use_percentage=False):
        """Place market order on Binance US with enhanced configuration"""
        try:
            # Input validation
            if not symbol or not isinstance(symbol, str):
                raise ValueError(f"Invalid symbol: {symbol}")
            if not side or not isinstance(side, str):
                raise ValueError(f"Invalid side: {side}")
            
            symbol = symbol.upper().strip()
            side = side.upper().strip()
            
            if side not in ['BUY', 'SELL']:
                raise ValueError(f"Invalid side: {side}. Must be 'BUY' or 'SELL'")
            
            logger.info(f"Starting place_market_order for {symbol} {side}")
            
            if not self.client:
                raise ConnectionError("Binance client not initialized")
            
            # Check emergency stop
            if not self.check_emergency_stop():
                raise RuntimeError("Emergency stop activated - trading disabled")
            
            # Validate symbol
            if not self.validate_symbol(symbol):
                raise ValueError(f"Symbol {symbol} not allowed")
            
            logger.info("Client initialized successfully")
            
            # Calculate quantity based on configuration
            quantity = 0.0
            try:
                if custom_quantity:
                    # Use custom quantity provided in webhook
                    try:
                        quantity = float(custom_quantity)
                        if quantity <= 0:
                            raise ValueError(f"Custom quantity must be positive: {quantity}")
                        logger.info(f"Using custom quantity: {quantity}")
                    except (ValueError, TypeError) as e:
                        raise ValueError(f"Invalid custom quantity format: {custom_quantity} - {e}")
                    
                elif use_percentage:
                    # Use percentage-based calculation
                    try:
                        quantity = self.calculate_percentage_quantity(symbol, side)
                        if quantity <= 0:
                            raise ValueError(f"Percentage calculation resulted in invalid quantity: {quantity}")
                        logger.info(f"Using percentage quantity: {quantity}")
                    except Exception as e:
                        logger.error(f"Percentage calculation failed: {e}")
                        raise ValueError(f"Failed to calculate percentage quantity: {e}")
                    
                else:
                    # Use fixed quantity (current safe approach)
                    if symbol in self.fixed_quantities:
                        quantity = self.fixed_quantities[symbol]
                        if quantity <= 0:
                            raise ValueError(f"Fixed quantity for {symbol} is invalid: {quantity}")
                    else:
                        quantity = 0.00001  # Default fallback
                        logger.warning(f"No fixed quantity for {symbol}, using default: {quantity}")
                    
                    logger.info(f"Using fixed quantity: {quantity}")
                
                # Final quantity validation
                if not isinstance(quantity, (int, float)) or quantity <= 0:
                    raise ValueError(f"Final quantity validation failed: {quantity}")
                
                # Validate against maximum limits
                if not self.validate_quantity(quantity, symbol):
                    raise ValueError(f"Quantity {quantity} exceeds maximum allowed for {symbol}")
                    
            except Exception as qty_error:
                logger.error(f"Quantity calculation/validation error: {qty_error}")
                raise
            
            # Apply precision formatting with error handling
            try:
                quantity = self.apply_precision_rules(quantity, symbol)
                if quantity <= 0:
                    raise ValueError(f"Precision formatting resulted in invalid quantity: {quantity}")
            except Exception as precision_error:
                logger.error(f"Precision formatting error: {precision_error}")
                raise ValueError(f"Failed to apply precision rules: {precision_error}")
            
            logger.info(f"Final calculated quantity: {quantity}")
            
            # Ensure quantity is formatted properly (no scientific notation)
            try:
                quantity_str = "{:.8f}".format(quantity)
                if '.' in quantity_str:
                    quantity_str = quantity_str.rstrip('0').rstrip('.')
                if not quantity_str or quantity_str == '0':
                    quantity_str = '0.00001'
                    logger.warning(f"Quantity string was empty or zero, using minimum: {quantity_str}")
            except Exception as format_error:
                logger.error(f"Quantity formatting error: {format_error}")
                raise ValueError(f"Failed to format quantity: {format_error}")
            
            logger.info(f"Final quantity string: '{quantity_str}'")
            
            # Place market order with comprehensive error handling
            try:
                if side == 'BUY':
                    logger.info(f"Placing BUY order for {symbol} with quantity {quantity_str}")
                    order = self.client.order_market_buy(
                        symbol=symbol,
                        quantity=quantity_str
                    )
                else:
                    logger.info(f"Placing SELL order for {symbol} with quantity {quantity_str}")
                    order = self.client.order_market_sell(
                        symbol=symbol,
                        quantity=quantity_str
                    )
                
                # Validate order response
                if not order or 'orderId' not in order:
                    raise ValueError(f"Invalid order response: {order}")
                
                logger.info(f"Order successful: {order['orderId']}")
                return {
                    'success': True,
                    'order_id': order['orderId'],
                    'symbol': symbol,
                    'side': side,
                    'quantity': quantity_str,
                    'status': order.get('status', 'UNKNOWN')
                }
                
            except BinanceAPIException as api_error:
                logger.error(f"Binance API error during order placement: {api_error}")
                raise RuntimeError(f"Binance API error: {api_error}")
            except BinanceOrderException as order_error:
                logger.error(f"Binance order error: {order_error}")
                raise RuntimeError(f"Order placement failed: {order_error}")
            except Exception as order_error:
                logger.error(f"Unexpected error during order placement: {order_error}")
                raise RuntimeError(f"Order placement failed: {order_error}")
            
        except (ValueError, TypeError) as validation_error:
            logger.error(f"Input validation error: {validation_error}")
            return {
                'success': False, 
                'error': f'Input validation failed: {validation_error}',
                'error_type': 'validation_error'
            }
        except (ConnectionError, RuntimeError) as system_error:
            logger.error(f"System error: {system_error}")
            return {
                'success': False, 
                'error': f'System error: {system_error}',
                'error_type': 'system_error'
            }
        except BinanceAPIException as api_error:
            logger.error(f"Binance API error: {api_error}")
            return {
                'success': False, 
                'error': f'Binance API error: {api_error}',
                'error_type': 'api_error'
            }
        except BinanceOrderException as order_error:
            logger.error(f"Binance order error: {order_error}")
            return {
                'success': False, 
                'error': f'Order error: {order_error}',
                'error_type': 'order_error'
            }
        except Exception as unexpected_error:
            logger.error(f"Unexpected error in place_market_order: {unexpected_error}")
            return {
                'success': False, 
                'error': f'Unexpected error: {unexpected_error}',
                'error_type': 'unexpected_error'
            }

# Global variables for services that need lazy initialization
client = None
ws_manager = None
trading_bot = None

def get_service_unavailable_response():
    """Generate consistent error response when service is unavailable"""
    is_banned = False
    ban_info = None
    rate_limit_info = None
    
    if rate_limiter and rate_limiter.banned_until:
        current_time = time.time()
        if current_time < rate_limiter.banned_until:
            is_banned = True
            ban_time = datetime.fromtimestamp(rate_limiter.banned_until)
            time_remaining = ban_time - datetime.now()
            ban_info = {
                'banned_until': ban_time.isoformat(),
                'time_remaining_seconds': int(time_remaining.total_seconds()),
                'message': 'IP banned due to excessive API requests. Use WebSocket streams for live data.'
            }
    
    # Add current rate limit status for debugging
    if rate_limiter:
        rate_limit_info = rate_limiter.get_rate_limit_status()
    
    if is_banned:
        response = {
            'status': 'error',
            'error': 'Binance API service temporarily unavailable - IP banned',
            'details': 'Trading service is currently offline due to IP ban from excessive API requests',
            'ban_info': ban_info,
            'rate_limit_status': rate_limit_info,
            'recommendation': 'Service will automatically resume when ban expires. Use WebSocket streams to avoid future bans.',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        return jsonify(response), 503
    else:
        response = {
            'status': 'error',
            'error': 'Binance API service temporarily unavailable',
            'details': 'Trading service is currently offline due to API connection issues or rate limiting',
            'rate_limit_status': rate_limit_info,
            'recommendation': 'Service should recover automatically. Consider using WebSocket streams for real-time data.',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        return jsonify(response), 503

def initialize_app_services():
    """Initialize app services in a controlled manner"""
    global client, ws_manager, trading_bot
    
    logger.info("Initializing app services...")
    
    try:
        # Initialize Binance client with enhanced configuration
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_SECRET_KEY')
        
        if api_key and api_secret:
            client = Client(
                api_key,
                api_secret,
                tld='us',  # Important: Use Binance US
                testnet=False,
                requests_params={
                    'timeout': 10,
                    'verify': True,
                }
            )
            
            # Test connection with a lightweight call
            server_time = client.get_server_time()
            logger.info("Enhanced Binance US client initialized successfully")
            logger.info(f"Connected to Binance US. Server time: {server_time}")
            
            # Initialize WebSocket manager only after successful client creation
            ws_manager = BinanceWebSocketManager(api_key=api_key, api_secret=api_secret, tld='us')
            ws_manager.initialize()
            ws_manager.connect()
            logger.info("WebSocket manager initialized successfully")
            
            # Initialize trading bot
            trading_bot = TradingBot(client)
            logger.info("TradingBot initialized successfully")
            
            # Initialize WebSocket streams in a separate thread
            def init_websockets():
                try:
                    if trading_bot:
                        trading_bot.initialize_websockets()
                    else:
                        logger.warning("Trading bot not available for WebSocket initialization")
                except Exception as e:
                    logger.error(f"Failed to initialize WebSocket streams: {e}")
            
            # Start balance streaming in background thread
            def init_balance_streaming():
                try:
                    logger.info("Starting balance streaming...")
                    start_balance_streaming()
                except Exception as e:
                    logger.error(f"Failed to start balance streaming: {e}")
            
            # Start P&L streaming in background thread
            def init_pnl_streaming():
                try:
                    logger.info("Starting P&L streaming...")
                    start_pnl_streaming()
                except Exception as e:
                    logger.error(f"Failed to start P&L streaming: {e}")
            
            # Start WebSocket initialization in background thread
            ws_thread = threading.Thread(target=init_websockets, daemon=True)
            ws_thread.start()
            logger.info("WebSocket initialization started in background")
            
            # Start balance streaming thread
            balance_thread = threading.Thread(target=init_balance_streaming, daemon=True)
            balance_thread.start()
            logger.info("Balance streaming started in background")
            
            # Start P&L streaming thread
            pnl_thread = threading.Thread(target=init_pnl_streaming, daemon=True)
            pnl_thread.start()
            logger.info("P&L streaming started in background")
            
            return True
        else:
            logger.warning("Binance API keys not found - running without Binance integration")
            trading_bot = TradingBot(None)
            return False
            
    except (BinanceAPIException, BinanceRequestException) as e:
        logger.error(f"Binance API error during initialization: {e}")
        
        # Handle IP ban specifically
        if hasattr(e, 'code') and getattr(e, 'code', None) == -1003:
            # Extract ban timestamp from error message
            import re
            ban_match = re.search(r'until (\d+)', str(e))
            if ban_match:
                ban_timestamp_ms = int(ban_match.group(1))
                ban_timestamp_s = ban_timestamp_ms / 1000
                ban_time = datetime.fromtimestamp(ban_timestamp_s)
                current_time = datetime.now()
                time_remaining = ban_time - current_time
                
                logger.error(f"IP BANNED until {ban_time} (in {time_remaining})")
                logger.error("Recommendation: Use WebSocket streams for live data to avoid future bans")
                
                # Set rate limiter ban time
                if 'rate_limiter' in globals():
                    rate_limiter.banned_until = ban_timestamp_s
        
        client = None
        ws_manager = None
        trading_bot = TradingBot(None)
        return False
    except Exception as e:
        logger.error(f"Failed to initialize Binance client: {e}")
        client = None
        ws_manager = None
        trading_bot = TradingBot(None)
        return False

@app.route('/', methods=['GET'])
def index():
    """Serve the frontend application"""
    return app.send_static_file('index.html')

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint - returns SystemStatus compatible data"""
    # Check WebSocket connection status
    websocket_connected = False
    if ws_manager:
        websocket_connected = ws_manager.is_connected
    
    # Check if IP is banned
    is_banned = False
    ban_info = None
    if rate_limiter and rate_limiter.banned_until:
        current_time = time.time()
        if current_time < rate_limiter.banned_until:
            is_banned = True
            ban_time = datetime.fromtimestamp(rate_limiter.banned_until)
            time_remaining = ban_time - datetime.now()
            ban_info = {
                'banned_until': ban_time.isoformat(),
                'time_remaining_seconds': int(time_remaining.total_seconds()),
                'message': 'IP banned due to excessive API requests. Use WebSocket streams for live data.'
            }
    
    # Determine system health based on various factors
    system_health = 'healthy'
    status_message = 'TradingView → Binance US Webhook Bot'
    
    if is_banned:
        system_health = 'error'
        status_message = 'Service temporarily unavailable - IP banned'
    elif not client:
        system_health = 'error'
        status_message = 'Binance API connection failed'
    elif not websocket_connected:
        system_health = 'warning'
        status_message = 'WebSocket connection issues'
    
    # Calculate uptime since application startup
    uptime_seconds = (datetime.now(timezone.utc) - APP_START_TIME).total_seconds()
    
    # Get trading status from trading bot
    trading_enabled = True
    if trading_bot:
        trading_enabled = not trading_bot.emergency_stop
    
    response = {
        'api_connected': True,  # If we're responding, API is connected
        'websocket_connected': websocket_connected,
        'last_heartbeat': datetime.now(timezone.utc).isoformat(),
        'trading_enabled': trading_enabled and not is_banned,
        'system_health': system_health,
        'uptime': int(uptime_seconds),
        # Legacy fields for backward compatibility
        'status': 'active' if not is_banned else 'banned',
        'message': status_message,
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'binance_status': 'connected' if client and not is_banned else 'disconnected'
    }
    
    if ban_info:
        response['ban_info'] = ban_info
    
    return jsonify(response)

@app.route('/webhook', methods=['POST'])
def webhook():
    """Main webhook endpoint for TradingView alerts"""
    try:
        # Get webhook data - handle different content types
        data = None
        content_type = request.content_type or 'unknown'
        logger.info(f"Received webhook with Content-Type: {content_type}")

        # Try to parse JSON first (preferred)
        if request.is_json:
            data = request.get_json()
            logger.info("Parsed as JSON data")
        else:
            # Handle other content types
            raw_data = request.get_data(as_text=True)
            logger.info(f"Raw webhook data: {raw_data}")

            # Try to parse as JSON even if content-type is wrong
            try:
                data = json.loads(raw_data)
                logger.info("Successfully parsed raw data as JSON")
            except json.JSONDecodeError:
                # Handle form data or plain text
                if request.form:
                    data = dict(request.form)
                    logger.info("Parsed as form data")
                else:
                    # Try to parse as simple key=value format
                    try:
                        # Handle TradingView's simple format like "coin:BTC action:BUY market_order:1"
                        if ':' in raw_data:
                            data = {}
                            pairs = raw_data.strip().split()
                            for pair in pairs:
                                if ':' in pair:
                                    key, value = pair.split(':', 1)
                                    data[key.strip()] = value.strip()
                            logger.info("Parsed as key:value format")
                        else:
                            logger.error(f"Could not parse webhook data: {raw_data}")
                            return jsonify({'error': 'Invalid data format'}), 400
                    except Exception as parse_error:
                        logger.error(f"Failed to parse webhook data: {parse_error}")
                        return jsonify({'error': 'Could not parse webhook data'}), 400

        if not data:
            logger.warning("No data received or could not parse data")
            return jsonify({'error': 'No data received'}), 400
        
        logger.info(f"Received webhook data: {json.dumps(data, indent=2)}")
        
        # Convert string values to appropriate types before validation
        processed_data = {}
        
        # Copy all data and convert numeric fields from strings to proper types
        for key, value in data.items():
            if key == 'quantity' and isinstance(value, str):
                try:
                    processed_data[key] = float(value) if value else None
                except (ValueError, TypeError):
                    processed_data[key] = None
            elif key == 'price' and isinstance(value, str):
                try:
                    processed_data[key] = float(value) if value else None
                except (ValueError, TypeError):
                    processed_data[key] = None
            elif key == 'market_order' and isinstance(value, str):
                try:
                    processed_data[key] = int(value) if value else None
                except (ValueError, TypeError):
                    processed_data[key] = None
            else:
                processed_data[key] = value
        
        # Validate webhook data using Pydantic
        try:
            validated_data = WebhookData(**processed_data)
            logger.info(f"Webhook data validation successful: {validated_data.dict()}")
        except ValidationError as e:
            logger.error(f"Webhook data validation failed: {e}")
            return jsonify({
                'error': 'Invalid webhook data format',
                'details': [{'field': err['loc'][0] if err['loc'] else 'unknown', 
                           'message': err['msg']} for err in e.errors()]
            }), 400
        except Exception as e:
            logger.error(f"Unexpected validation error: {e}")
            return jsonify({'error': 'Data validation failed'}), 400
        
        # Use validated data for further processing
        data = validated_data.dict()
        
        # Validate webhook secret (optional security)
        import urllib.parse
        webhook_secret = request.headers.get('X-Webhook-Secret')
        if webhook_secret:
            webhook_secret = urllib.parse.unquote(webhook_secret)

        # Also check URL parameters for secret (TradingView compatibility)
        if not webhook_secret:
            webhook_secret = request.args.get('secret')

        expected_secret = os.getenv('WEBHOOK_SECRET', 'testsecret')  # Default for testing

        logger.info(f"Webhook secret: '{webhook_secret}', Expected: '{expected_secret}'")

        if expected_secret and webhook_secret != expected_secret:
            logger.warning(f"Secret mismatch - Received: '{webhook_secret}', Expected: '{expected_secret}'")
            logger.warning("Invalid webhook secret")
            return jsonify({'error': 'Unauthorized'}), 401
        
        # Extract required fields - support both old and new formats
        action = data.get('action', '').upper()
        symbol = data.get('symbol', '').upper()
        coin = data.get('coin', '').upper()
        
        # Debug prints to console
        print(f"DEBUG: action='{action}', symbol='{symbol}', coin='{coin}'")
        
        # Handle new Pine Script format
        if coin and not symbol:
            # New format: {"coin":"BTC", "action":"BUY", "market_order":"1"}
            symbol = coin + "USD"  # Convert BTC to BTCUSD
            message = f"Pine Script alert - {action}"
            print(f"DEBUG: New format detected - symbol set to '{symbol}'")
        elif not coin and not symbol:
            print("DEBUG: Missing both symbol and coin")
            return jsonify({'error': 'Missing symbol or coin'}), 400
        
        if not action:
            print("DEBUG: Missing action")
            return jsonify({'error': 'Missing action'}), 400
        
        if action not in ['BUY', 'SELL']:
            print(f"DEBUG: Invalid action: {action}")
            return jsonify({'error': 'Invalid action. Use BUY or SELL'}), 400
        
        print(f"DEBUG: Validation passed - action='{action}', symbol='{symbol}'")
        
        # Optional: Get custom quantity
        quantity = data.get('quantity')
        
        # Execute trade
        if not trading_bot:
            return jsonify({'error': 'Trading bot not initialized'}), 500
        
  # Always use percentage-based trading from .env configuration
        use_percentage = True
        custom_quantity = data.get('quantity')
        
        logger.info(f"Executing trade - action: {action}, symbol: {symbol}, use_percentage: {use_percentage}, custom_quantity: {custom_quantity}")
        
        # Get current market price for verification
        try:
            ticker = trading_bot.client.get_symbol_ticker(symbol=symbol)
            current_price = float(ticker['price'])
            logger.info(f"Current market price for {symbol}: ${current_price}")
        except Exception as e:
            logger.warning(f"Could not get current price: {e}")
            current_price = None
        
        # Place the market order
        result = trading_bot.place_market_order(
            symbol=symbol, 
            side=action, 
            custom_quantity=custom_quantity,
            use_percentage=use_percentage
        )
        
        # Add price information to result
        if current_price:
            result['market_price'] = current_price
        
        if result['success']:
            response = {
                'status': 'success',
                'message': f'{action} order executed successfully',
                'order_details': result,
                'market_price': current_price,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            return jsonify(response), 200
        else:
            response = {
                'status': 'error',
                'message': 'Order execution failed',
                'error': result['error'],
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            return jsonify(response), 400
        
    except Exception as e:
        logger.error(f"Webhook error: {e}")
        return jsonify({
            'status': 'error',
            'message': 'Internal server error',
            'error': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 500

@app.route('/debug', methods=['POST'])
def debug_webhook():
    """Debug endpoint to see what TradingView is sending"""
    try:
        debug_info = {
            'content_type': request.content_type,
            'headers': dict(request.headers),
            'is_json': request.is_json,
            'raw_data': request.get_data(as_text=True),
            'form_data': dict(request.form) if request.form else None,
            'args': dict(request.args) if request.args else None,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

        # Try to parse as JSON
        try:
            json_data = request.get_json()
            debug_info['json_data'] = json_data
        except Exception as e:
            debug_info['json_parse_error'] = str(e)

        logger.info(f"Debug webhook info: {json.dumps(debug_info, indent=2)}")
        return jsonify(debug_info)

    except Exception as e:
        logger.error(f"Debug webhook error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/test', methods=['POST'])
def test_order():
    """Test endpoint for manual testing"""
    try:
        data = request.get_json() or {}
        action = data.get('action', 'BUY').upper()
        symbol = data.get('symbol', 'BTCUSDT').upper()

        if not trading_bot:
            return jsonify({'error': 'Trading bot not initialized'}), 500

        # Place test order with percentage-based quantity
        result = trading_bot.place_market_order(symbol, action, use_percentage=True)
        return jsonify(result)

    except Exception as e:
        logger.error(f"Test order error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/balance', methods=['GET'])
def get_balance():
    """Get account balance"""
    try:
        if not client:
            return get_service_unavailable_response()
        
        account = client.get_account()
        balances = []
        
        for balance in account['balances']:
            if float(balance['free']) > 0 or float(balance['locked']) > 0:
                balances.append({
                    'asset': balance['asset'],
                    'free': balance['free'],
                    'locked': balance['locked']
                })
        
        return jsonify({
            'balances': balances,
            'timestamp': datetime.now(timezone.utc).isoformat()
        })
        
    except Exception as e:
        logger.error(f"Balance error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/config', methods=['GET'])
def get_config():
    """Get current trading configuration"""
    try:
        if not trading_bot:
            return jsonify({'error': 'Trading bot not initialized'}), 500
        
        config = {
            'enabled': not trading_bot.emergency_stop,  # Trading enabled when emergency stop is false
            'emergency_stop': trading_bot.emergency_stop,
            'allowed_symbols': trading_bot.allowed_symbols,
            'quantity_percentage': trading_bot.quantity_percentage,
            'max_quantities': trading_bot.max_quantities,
            'fixed_quantities': trading_bot.fixed_quantities,
            'max_position_size': trading_bot.max_quantities.get('BTCUSDT', 0.1),  # Use BTCUSDT as default
            'risk_percentage': trading_bot.quantity_percentage,
            'stop_loss_percentage': trading_bot.stop_loss_percentage if trading_bot.stop_loss_percentage is not None else 'N/A',
            'take_profit_percentage': trading_bot.take_profit_percentage if trading_bot.take_profit_percentage is not None else 'N/A',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        return jsonify(config)
        
    except Exception as e:
        logger.error(f"Config error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/rate-limits', methods=['GET'])
def get_rate_limits():
    """Get current rate limit status"""
    try:
        if not rate_limiter:
            return jsonify({'error': 'Rate limiter not initialized'}), 500
        
        status = rate_limiter.get_rate_limit_status()
        
        # Add WebSocket connection status
        ws_status = {
            'is_connected': trading_bot.ws_manager.is_connected if trading_bot and trading_bot.ws_manager else False,
            'active_streams': len(trading_bot.ws_manager.active_streams) if trading_bot and trading_bot.ws_manager else 0,
            'cached_prices': len(trading_bot.ws_manager.price_data) if trading_bot and trading_bot.ws_manager else 0,
        }
        
        # Add ban status
        ban_status = {
            'is_banned': rate_limiter.banned_until and time.time() < rate_limiter.banned_until,
            'banned_until': rate_limiter.banned_until,
            'time_remaining': max(0, rate_limiter.banned_until - time.time()) if rate_limiter.banned_until else 0
        }
        
        response = {
            'rate_limits': status,
            'websocket_status': ws_status,
            'ban_status': ban_status,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Rate limits error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/circuit-breaker-status', methods=['GET'])
def get_circuit_breaker_status():
    """Get circuit breaker status for frontend monitoring"""
    try:
        # This endpoint provides backend status for frontend circuit breaker decisions
        current_time = time.time()
        
        # Check if we're currently banned
        is_banned = rate_limiter and rate_limiter.banned_until and current_time < rate_limiter.banned_until
        
        # Get rate limit status
        rate_status = rate_limiter.get_rate_limit_status() if rate_limiter else {}
        
        # Calculate service health score (0-100)
        health_score = 100
        if is_banned:
            health_score = 0
        elif rate_status:
            # Reduce health score based on rate limit usage
            max_usage = max((status.get('usage_percent', 0) for status in rate_status.values()), default=0)
            if max_usage > 90:
                health_score = 10
            elif max_usage > 75:
                health_score = 50
            elif max_usage > 50:
                health_score = 75
        
        response = {
            'service_available': not is_banned and (client is not None),
            'health_score': health_score,
            'is_banned': is_banned,
            'ban_time_remaining': max(0, rate_limiter.banned_until - current_time) if is_banned and rate_limiter.banned_until else 0,
            'rate_limit_status': rate_status,
            'websocket_available': trading_bot and trading_bot.ws_manager and trading_bot.ws_manager.is_connected,
            'recommendation': 'use_websocket' if is_banned else 'normal_operation',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Circuit breaker status error: {e}")
        return jsonify({
            'service_available': False,
            'health_score': 0,
            'error': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 500

@app.route('/prices', methods=['GET'])
def get_prices():
    """Get current market prices for all symbols"""
    try:
        if not trading_bot:
            return jsonify({'error': 'Trading bot not initialized'}), 500
        
        # Get symbols from query parameter or use default list
        symbols_param = request.args.get('symbols', '')
        if symbols_param:
            symbols = [s.strip().upper() for s in symbols_param.split(',')]
        else:
            # Default symbols based on allowed symbols and common pairs
            symbols = list(trading_bot.allowed_symbols) + ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
            symbols = list(set(symbols))  # Remove duplicates
        
        prices = {}
        
        # Get prices for all requested symbols
        for symbol in symbols:
            try:
                # Skip invalid symbols that would cause API errors
                if not trading_bot.validate_symbol(symbol):
                    logger.warning(f"Skipping invalid symbol: {symbol}")
                    continue
                    
                # First try to get cached price from WebSocket
                if trading_bot and trading_bot.ws_manager:
                    cached_price = trading_bot.ws_manager.get_cached_price(symbol)
                    if cached_price is not None:
                        prices[symbol] = cached_price
                        continue
                
                # Fallback to REST API with rate limiting
                if trading_bot and not trading_bot.rate_limiter.is_rate_limited('ticker'):
                    ticker = trading_bot.client.get_symbol_ticker(symbol=symbol)
                    prices[symbol] = float(ticker['price'])
                    trading_bot.rate_limiter.record_request('ticker')
                else:
                    logger.warning(f"Rate limited, skipping REST API call for {symbol}")
                    continue
            except Exception as e:
                logger.warning(f"Could not get price for {symbol}: {e}")
                # Don't include symbols that fail
                continue
        
        response = {
            'prices': prices,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'count': len(prices)
        }
        
        logger.info(f"Retrieved prices for {len(prices)} symbols")
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Prices error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/pnl', methods=['GET'])
def get_pnl_data_raw():
    """Get profit and loss data as raw dict (for WebSocket use)"""
    if not trading_bot:
        raise Exception('Trading bot not initialized')
    
    if not client:
        raise Exception('Binance API service temporarily unavailable')
    
    # Get account information for current positions
    account_info = trading_bot.client.get_account()
    
    # Initialize totals
    total_unrealized_pnl = 0.0
    total_realized_pnl = 0.0
    pnl_data = []
    
    # Get current prices for PnL calculation
    current_prices = {}
    for balance in account_info['balances']:
        asset = balance['asset']
        if asset not in ['USDT', 'USD', 'USDC'] and float(balance['free']) > 0:
            try:
                symbol = f"{asset}USDT"
                # Use WebSocket cached price first, fallback to REST API
                if trading_bot.ws_manager:
                    cached_price = trading_bot.ws_manager.get_cached_price(symbol)
                    if cached_price is not None:
                        current_prices[symbol] = cached_price
                        continue
                
                # Fallback to REST API with rate limiting
                if not trading_bot.rate_limiter.is_rate_limited('ticker'):
                    ticker = trading_bot.client.get_symbol_ticker(symbol=symbol)
                    current_prices[symbol] = float(ticker['price'])
                    trading_bot.rate_limiter.record_request('ticker')
            except Exception as e:
                logger.warning(f"Could not get price for {asset}USDT: {e}")
                continue
    
    # Calculate P&L for each asset with positions
    for balance in account_info['balances']:
        asset = balance['asset']
        free_balance = float(balance['free'])
        locked_balance = float(balance['locked'])
        total_balance = free_balance + locked_balance
        
        if asset not in ['USDT', 'USD', 'USDC'] and total_balance > 0:
            symbol = f"{asset}USDT"
            if symbol in current_prices:
                current_price = current_prices[symbol]
                current_value = total_balance * current_price
                
                try:
                    # Get complete trade history for FIFO calculation
                    trades = client.get_my_trades(symbol=symbol, limit=1000)
                    if trades:
                        # Calculate realized and unrealized P&L using FIFO
                        realized_pnl, unrealized_pnl, avg_cost_basis = calculate_fifo_pnl(
                            trades, total_balance, current_price
                        )
                        
                        total_realized_pnl += realized_pnl
                        total_unrealized_pnl += unrealized_pnl
                        
                        pnl_data.append({
                            'timestamp': datetime.now(timezone.utc).isoformat(),
                            'symbol': symbol,
                            'asset': asset,
                            'quantity': str(total_balance),
                            'current_price': current_price,
                            'avg_cost_basis': avg_cost_basis,
                            'current_value': current_value,
                            'realized_pnl': realized_pnl,
                            'unrealized_pnl': unrealized_pnl,
                            'total_pnl': realized_pnl + unrealized_pnl
                        })
                    else:
                        # No trade history available, assume zero cost basis
                        unrealized_pnl = current_value  # All gains if no cost basis
                        total_unrealized_pnl += unrealized_pnl
                        
                        pnl_data.append({
                            'timestamp': datetime.now(timezone.utc).isoformat(),
                            'symbol': symbol,
                            'asset': asset,
                            'quantity': str(total_balance),
                            'current_price': current_price,
                            'avg_cost_basis': 0.0,
                            'current_value': current_value,
                            'realized_pnl': 0.0,
                            'unrealized_pnl': unrealized_pnl,
                            'total_pnl': unrealized_pnl
                        })
                except Exception as e:
                    logger.warning(f"Could not calculate PnL for {symbol}: {e}")
                    continue
    
    # Calculate total P&L
    total_pnl = total_realized_pnl + total_unrealized_pnl
    
    response = {
        'success': True,
        'pnl': pnl_data,
        'totalPnL': total_pnl,
        'realized_pnl': total_realized_pnl,
        'unrealized_pnl': total_unrealized_pnl,
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'positions_count': len(pnl_data)
    }
    
    logger.info(f"Retrieved PnL data with {len(pnl_data)} positions, total PnL: {total_pnl:.2f} USDT")
    return response

def get_pnl_data():
    """Get profit and loss data including realized and unrealized PnL using FIFO calculation"""
    try:
        result = get_pnl_data_raw()
        return jsonify(result)
    except Exception as e:
        logger.error(f"PnL error: {e}")
        return jsonify({'error': str(e)}), 500

# Legacy function kept for compatibility
def get_pnl_data_old():
    """Get profit and loss data including realized and unrealized PnL using FIFO calculation"""
    try:
        if not trading_bot:
            return jsonify({'error': 'Trading bot not initialized'}), 500
        
        if not client:
            return jsonify({
                'error': 'Binance API service temporarily unavailable',
                'details': 'Trading service is currently offline due to API restrictions'
            }), 503
        
        # Get account information for current positions
        account_info = trading_bot.client.get_account()
        
        # Initialize totals
        total_unrealized_pnl = 0.0
        total_realized_pnl = 0.0
        pnl_data = []
        
        # Get current prices for PnL calculation
        current_prices = {}
        for balance in account_info['balances']:
            asset = balance['asset']
            if asset not in ['USDT', 'USD', 'USDC'] and float(balance['free']) > 0:
                try:
                    symbol = f"{asset}USDT"
                    # Use WebSocket cached price first, fallback to REST API
                    if trading_bot.ws_manager:
                        cached_price = trading_bot.ws_manager.get_cached_price(symbol)
                        if cached_price is not None:
                            current_prices[symbol] = cached_price
                            continue
                    
                    # Fallback to REST API with rate limiting
                    if not trading_bot.rate_limiter.is_rate_limited('ticker'):
                        ticker = trading_bot.client.get_symbol_ticker(symbol=symbol)
                        current_prices[symbol] = float(ticker['price'])
                        trading_bot.rate_limiter.record_request('ticker')
                except Exception as e:
                    logger.warning(f"Could not get price for {asset}USDT: {e}")
                    continue
        
        # Calculate P&L for each asset with positions
        for balance in account_info['balances']:
            asset = balance['asset']
            free_balance = float(balance['free'])
            locked_balance = float(balance['locked'])
            total_balance = free_balance + locked_balance
            
            if asset not in ['USDT', 'USD', 'USDC'] and total_balance > 0:
                symbol = f"{asset}USDT"
                if symbol in current_prices:
                    current_price = current_prices[symbol]
                    current_value = total_balance * current_price
                    
                    try:
                        # Get complete trade history for FIFO calculation
                        trades = client.get_my_trades(symbol=symbol, limit=1000)
                        if trades:
                            # Calculate realized and unrealized P&L using FIFO
                            realized_pnl, unrealized_pnl, avg_cost_basis = calculate_fifo_pnl(
                                trades, total_balance, current_price
                            )
                            
                            total_realized_pnl += realized_pnl
                            total_unrealized_pnl += unrealized_pnl
                            
                            pnl_data.append({
                                'timestamp': datetime.now(timezone.utc).isoformat(),
                                'symbol': symbol,
                                'asset': asset,
                                'quantity': str(total_balance),
                                'current_price': current_price,
                                'avg_cost_basis': avg_cost_basis,
                                'current_value': current_value,
                                'realized_pnl': realized_pnl,
                                'unrealized_pnl': unrealized_pnl,
                                'total_pnl': realized_pnl + unrealized_pnl
                            })
                        else:
                            # No trade history available, assume zero cost basis
                            unrealized_pnl = current_value  # All gains if no cost basis
                            total_unrealized_pnl += unrealized_pnl
                            
                            pnl_data.append({
                                'timestamp': datetime.now(timezone.utc).isoformat(),
                                'symbol': symbol,
                                'asset': asset,
                                'quantity': str(total_balance),
                                'current_price': current_price,
                                'avg_cost_basis': 0.0,
                                'current_value': current_value,
                                'realized_pnl': 0.0,
                                'unrealized_pnl': unrealized_pnl,
                                'total_pnl': unrealized_pnl
                            })
                    except Exception as e:
                        logger.warning(f"Could not calculate PnL for {symbol}: {e}")
                        continue
        
        # Calculate total P&L
        total_pnl = total_realized_pnl + total_unrealized_pnl
        
        response = {
            'success': True,
            'pnl': pnl_data,
            'totalPnL': total_pnl,
            'realized_pnl': total_realized_pnl,
            'unrealized_pnl': total_unrealized_pnl,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'positions_count': len(pnl_data)
        }
        
        logger.info(f"Retrieved PnL data with {len(pnl_data)} positions, total PnL: {total_pnl:.2f} USDT")
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"PnL error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/emergency-stop', methods=['POST'])
def emergency_stop():
    """Toggle emergency stop (admin only)"""
    try:
        if not trading_bot:
            return jsonify({'error': 'Trading bot not initialized'}), 500
        
        # Toggle emergency stop
        trading_bot.emergency_stop = not trading_bot.emergency_stop
        
        return jsonify({
            'success': True,
            'emergency_stop': trading_bot.emergency_stop,
            'message': 'Emergency stop ' + ('activated' if trading_bot.emergency_stop else 'deactivated'),
            'timestamp': datetime.now(timezone.utc).isoformat()
        })
        
    except Exception as e:
        logger.error(f"Emergency stop error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/trades', methods=['GET'])
def get_trade_history():
    """Get trade history with pagination"""
    try:
        if not client:
            return get_service_unavailable_response()
        
        # Get pagination parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))
        symbol = request.args.get('symbol', '')
        
        # Validate limit
        if limit > 1000:
            limit = 1000
        if limit < 1:
            limit = 50
            
        # Get all trades for allowed symbols
        all_trades = []
        
        if symbol:
            # Get trades for specific symbol
            symbols_to_query = [symbol.upper()] if trading_bot and symbol.upper() in trading_bot.allowed_symbols else []
        else:
            # Get trades for all allowed symbols
            symbols_to_query = trading_bot.allowed_symbols if trading_bot else []
        
        for sym in symbols_to_query:
            try:
                # Get recent trades for this symbol
                trades = client.get_my_trades(symbol=sym, limit=500)
                
                for trade in trades:
                    trade_item = {
                        'id': str(trade['id']),
                        'symbol': trade['symbol'],
                        'side': 'BUY' if trade['isBuyer'] else 'SELL',
                        'quantity': trade['qty'],
                        'price': trade['price'],
                        'timestamp': datetime.fromtimestamp(trade['time'] / 1000, tz=timezone.utc).isoformat(),
                        'status': 'FILLED',  # All returned trades are filled
                        'commission': trade.get('commission', '0'),
                        'commission_asset': trade.get('commissionAsset', '')
                    }
                    all_trades.append(trade_item)
                    
            except Exception as e:
                logger.warning(f"Could not get trades for {sym}: {e}")
                continue
        
        # Sort trades by timestamp (newest first)
        all_trades.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # Calculate pagination
        total_count = len(all_trades)
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_trades = all_trades[start_idx:end_idx]
        
        response = {
            'status': 'success',
            'trades': paginated_trades,
            'total_count': total_count,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        logger.info(f"Retrieved {len(paginated_trades)} trades (page {page}, total: {total_count})")
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Trade history error: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 500

@app.route('/api/errors', methods=['POST'])
def log_frontend_error():
    """Log frontend errors for centralized error tracking"""
    try:
        error_data = request.get_json()
        if not error_data:
            return jsonify({'error': 'No error data provided'}), 400
        
        # Validate error data using Pydantic
        try:
            validated_error = ErrorLogData(**error_data)
            logger.info(f"Error data validation successful")
        except ValidationError as e:
            logger.error(f"Error data validation failed: {e}")
            return jsonify({
                'error': 'Invalid error data format',
                'details': [{'field': err['loc'][0] if err['loc'] else 'unknown', 
                           'message': err['msg']} for err in e.errors()]
            }), 400
        except Exception as e:
            logger.error(f"Unexpected error validation error: {e}")
            return jsonify({'error': 'Error data validation failed'}), 400
        
        # Use validated data
        error_data = validated_error.dict()
        
        # Log the frontend error
        logger.error(f"Frontend Error: {error_data.get('context', 'Unknown error')}")
        logger.error(f"URL: {error_data.get('url', 'Unknown')}")
        logger.error(f"User Agent: {error_data.get('userAgent', 'Unknown')}")
        logger.error(f"Timestamp: {error_data.get('timestamp', 'Unknown')}")
        
        return jsonify({
            'success': True,
            'message': 'Error logged successfully',
            'timestamp': datetime.now(timezone.utc).isoformat()
        })
        
    except Exception as e:
        logger.error(f"Failed to log frontend error: {e}")
        return jsonify({'error': 'Failed to log error'}), 500

# WebSocket event handlers
@socketio.on('connect')
def handle_connect():
    logger.info('Client connected')
    emit('status', {'message': 'Connected to trading server'})

@socketio.on('disconnect')
def handle_disconnect():
    logger.info('Client disconnected')

@socketio.on('subscribe_prices')
def handle_subscribe_prices(data):
    symbols = data.get('symbols', [])
    logger.info(f'Client subscribed to price updates for: {symbols}')
    # Start price streaming for subscribed symbols
    start_price_streaming(symbols)
    return {'status': 'subscribed', 'message': f'Price updates subscription confirmed for {len(symbols)} symbols'}

@socketio.on('subscribe_balance')
def handle_subscribe_balance():
    logger.info('Client subscribed to balance updates')
    # Start balance streaming
    start_balance_streaming()
    return {'status': 'subscribed', 'message': 'Balance updates subscription confirmed'}

# Cache for balance data to avoid slow API calls
balance_cache = {
    'data': None,
    'timestamp': 0,
    'ttl': 30  # 30 seconds cache
}

@socketio.on('balance')
def handle_balance_request(data):
    """Handle balance request from frontend with rate limiting and ban detection"""
    try:
        logger.info(f'Balance request received: {data}')
        
        # Check for IP ban first
        if rate_limiter and rate_limiter.banned_until:
            current_time = time.time()
            if current_time < rate_limiter.banned_until:
                ban_time = datetime.fromtimestamp(rate_limiter.banned_until)
                logger.warning(f'API is banned until {ban_time}, rejecting balance request')
                return {
                    'success': False, 
                    'error': 'API temporarily banned due to rate limits',
                    'banned_until': ban_time.isoformat(),
                    'retry_after': int(rate_limiter.banned_until - current_time)
                }
        
        # Check if we should subscribe to balance updates
        if data and data.get('subscribe', False):
            logger.info('Starting balance streaming for client')
            start_balance_streaming()
        
        # Check cache first to avoid slow API calls
        current_time = time.time()
        if (balance_cache['data'] is not None and 
            current_time - balance_cache['timestamp'] < balance_cache['ttl']):
            logger.info('Returning cached balance data')
            return balance_cache['data']
        
        # Check rate limits before making API call
        if rate_limiter and rate_limiter.is_rate_limited('account', weight=10):
            logger.warning('Rate limited, rejecting balance request')
            # Return cached data if available, even if stale
            if balance_cache['data'] is not None:
                logger.info('Returning stale cached balance due to rate limit')
                return balance_cache['data']
            return {
                'success': False, 
                'error': 'Rate limited, please try again later',
                'retry_after': 60
            }
        
        # Get current account balance
        if client is None:
            logger.error('Binance client not initialized')
            return {'success': False, 'error': 'Client not initialized'}
        
        # Get current balance with proper error handling and timeout
        try:
            # Set a shorter timeout for the API call using Windows-compatible threading
            import threading
            
            timeout_occurred = threading.Event()
            
            def timeout_handler():
                timeout_occurred.set()
            
            # Set 10-second timeout for API call (increased from 8s)
            timer = threading.Timer(10.0, timeout_handler)
            timer.start()
            
            try:
                account_info = client.get_account()
                if timeout_occurred.is_set():
                    raise TimeoutError("API call timed out")
            finally:
                timer.cancel()  # Cancel the timer
            
            # Record the API request for rate limiting
            if rate_limiter:
                rate_limiter.record_request('account', weight=10)
            
        except TimeoutError as e:
            logger.error(f'Balance API call timed out: {e}')
            # Return cached data if available
            if balance_cache['data'] is not None:
                logger.info('Returning stale cached balance due to timeout')
                return balance_cache['data']
            return {'success': False, 'error': 'API call timed out, please try again'}
        except BinanceAPIException as e:
            # Handle specific rate limit errors
            if e.code == -1003:  # Too many requests
                logger.error(f'Rate limit exceeded: {e}')
                if rate_limiter:
                    retry_after = 300  # 5 minutes default
                    rate_limiter.handle_rate_limit_error(e, retry_after)
                # Return cached data if available
                if balance_cache['data'] is not None:
                    logger.info('Returning stale cached balance due to rate limit')
                    return balance_cache['data']
                return {
                    'success': False, 
                    'error': 'Rate limit exceeded, service temporarily suspended',
                    'retry_after': retry_after
                }
            elif e.code == -1021:  # Timestamp outside recv window
                logger.error(f'Timestamp error: {e}')
                return {'success': False, 'error': 'Server time synchronization error'}
            else:
                logger.error(f'Binance API error: {e}')
                return {'success': False, 'error': f'API error: {e.message}'}
        
        balances = []
        
        for balance in account_info['balances']:
            free_balance = float(balance['free'])
            locked_balance = float(balance['locked'])
            total_balance = free_balance + locked_balance
            
            if total_balance > 0:  # Only include non-zero balances
                balances.append({
                    'asset': balance['asset'],
                    'free': free_balance,
                    'locked': locked_balance,
                    'total': total_balance
                })
        
        response = {
            'success': True,
            'balances': balances,
            'timestamp': datetime.now().isoformat()
        }
        
        # Cache the successful response
        balance_cache['data'] = response
        balance_cache['timestamp'] = time.time()
        
        logger.info(f'Returning balance data: {len(balances)} assets (cached for {balance_cache["ttl"]}s)')
        return response
        
    except Exception as e:
        logger.error(f'Unexpected error handling balance request: {e}')
        return {'success': False, 'error': 'Internal server error'}

@socketio.on('subscribe_pnl')
def handle_subscribe_pnl():
    logger.info('Client subscribed to PnL updates')
    # PnL streaming is already running as a daemon thread
    return {'status': 'subscribed', 'message': 'PnL updates subscription confirmed'}

# Cache for PnL data to avoid slow API calls
pnl_cache = {
    'data': None,
    'timestamp': 0,
    'ttl': 15  # 15 seconds cache for more frequent updates
}

@socketio.on('pnl')
def handle_pnl_message(data):
    """Handle generic PnL messages with acknowledgment and caching"""
    try:
        logger.info(f'Client sent PnL message: {data}')
        
        # Check if this is a subscription request
        if isinstance(data, dict) and data.get('subscribe'):
            logger.info('Client subscribed to PnL updates via pnl message')
            # PnL streaming is already running as a daemon thread
            return {'status': 'subscribed', 'message': 'PnL updates subscription confirmed'}
        
        # Check if this is a data request
        if isinstance(data, dict) and data.get('request_data'):
            # Check cache first
            current_time = time.time()
            if (pnl_cache['data'] is not None and 
                current_time - pnl_cache['timestamp'] < pnl_cache['ttl']):
                logger.info('Returning cached PnL data')
                return pnl_cache['data']
            
            # Fetch fresh PnL data with timeout
            try:
                import threading
                
                timeout_occurred = threading.Event()
                
                def timeout_handler():
                    timeout_occurred.set()
                
                # Set 10-second timeout for API call (increased from 8s)
                timer = threading.Timer(10.0, timeout_handler)
                timer.start()
                
                try:
                    # Get PnL data directly (not the Flask response)
                    pnl_response = get_pnl_data_raw()  # Call raw data function
                    if timeout_occurred.is_set():
                        raise TimeoutError("PnL API call timed out")
                    
                    response = {
                        'status': 'success',
                        'data': pnl_response,
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    # Cache the response
                    pnl_cache['data'] = response
                    pnl_cache['timestamp'] = time.time()
                    logger.info('Cached fresh PnL data')
                    
                    return response
                    
                finally:
                    timer.cancel()  # Cancel the timer
                    
            except TimeoutError as e:
                logger.error(f'PnL API call timed out: {e}')
                # Return cached data if available
                if pnl_cache['data'] is not None:
                    logger.info('Returning stale cached PnL due to timeout')
                    return pnl_cache['data']
                return {'status': 'error', 'message': 'PnL data request timed out'}
            except Exception as e:
                logger.error(f'Error fetching PnL data: {e}')
                # Return cached data if available
                if pnl_cache['data'] is not None:
                    logger.info('Returning stale cached PnL due to error')
                    return pnl_cache['data']
                return {'status': 'error', 'message': f'Failed to fetch PnL data: {str(e)}'}
        
        # Handle other PnL message types if needed
        return {'status': 'success', 'message': 'PnL message processed'}
        
    except Exception as e:
        logger.error(f'Error handling PnL message: {e}')
        return {'status': 'error', 'message': f'Failed to process PnL message: {str(e)}'}

# Real-time data streaming functions
def start_price_streaming(symbols):
    """WebSocket-first price streaming with REST fallback"""
    def stream_prices():
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while True:
            try:
                # Check for IP ban first - this is critical
                if rate_limiter and rate_limiter.banned_until:
                    current_time = time.time()
                    if current_time < rate_limiter.banned_until:
                        wait_time = rate_limiter.banned_until - current_time
                        ban_time = datetime.fromtimestamp(rate_limiter.banned_until)
                        logger.warning(f"API banned until {ban_time}, price streaming suspended for {wait_time:.0f}s")
                        time.sleep(min(wait_time, 600))  # Max 10 min wait per cycle
                        continue
                
                if symbols and trading_bot and trading_bot.ws_manager:
                    # Get prices from WebSocket cache first
                    prices = {}
                    websocket_prices = trading_bot.ws_manager.get_all_prices()
                    
                    for symbol in symbols:
                        if trading_bot and trading_bot.ws_manager:
                            cached_price = trading_bot.ws_manager.get_cached_price(symbol)
                            if cached_price is not None:
                                prices[symbol] = {
                                    'symbol': symbol,
                                    'price': cached_price,
                                    'timestamp': datetime.now().isoformat(),
                                    'source': 'websocket'
                                }
                        else:
                            # REST API fallback with rate limiting
                            if not trading_bot.rate_limiter.is_rate_limited('ticker'):
                                try:
                                    if client is None:
                                        logger.error("Binance client not initialized")
                                        continue
                                    ticker = client.get_symbol_ticker(symbol=symbol)
                                    prices[symbol] = {
                                        'symbol': symbol,
                                        'price': float(ticker['price']),
                                        'timestamp': datetime.now().isoformat(),
                                        'source': 'rest'
                                    }
                                    trading_bot.rate_limiter.record_request('ticker')
                                    consecutive_errors = 0  # Reset on success
                                    
                                except BinanceAPIException as e:
                                    consecutive_errors += 1
                                    
                                    # Handle specific rate limit errors
                                    if e.code == -1003:  # Too many requests
                                        logger.error(f'Rate limit exceeded in price streaming for {symbol}: {e}')
                                        if trading_bot.rate_limiter:
                                            retry_after = 300  # 5 minutes for price streaming
                                            trading_bot.rate_limiter.handle_rate_limit_error(e, retry_after)
                                    elif e.code == -1021:  # Timestamp outside recv window
                                        logger.error(f'Timestamp error in price streaming for {symbol}: {e}')
                                    else:
                                        logger.warning(f'Binance API error for {symbol}: {e}')
                                        
                                except Exception as e:
                                    consecutive_errors += 1
                                    logger.warning(f'REST fallback failed for {symbol}: {e}')
                            else:
                                logger.warning(f"Rate limited, skipping REST API for {symbol}")
                    
                    if prices:
                        socketio.emit('price_update', prices)
                        
                time.sleep(2)  # Reduced frequency since we're using WebSockets
            except Exception as e:
                logger.error(f'Error in price streaming: {e}')
                time.sleep(5)
    
    threading.Thread(target=stream_prices, daemon=True).start()

def start_balance_streaming():
    def stream_balance():
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while True:
            try:
                # Check for IP ban first - this is critical
                if rate_limiter and rate_limiter.banned_until:
                    current_time = time.time()
                    if current_time < rate_limiter.banned_until:
                        wait_time = rate_limiter.banned_until - current_time
                        ban_time = datetime.fromtimestamp(rate_limiter.banned_until)
                        logger.warning(f"API banned until {ban_time}, balance streaming suspended for {wait_time:.0f}s")
                        time.sleep(min(wait_time, 600))  # Max 10 min wait per cycle
                        continue
                
                if client is None:
                    logger.error("Binance client not initialized")
                    time.sleep(30)
                    continue
                
                # Check rate limits before making API call
                if rate_limiter.is_rate_limited('account', weight=10):
                    logger.warning("Rate limited, skipping balance update")
                    time.sleep(60)  # Increased wait time
                    continue
                
                # Make API call with proper error handling
                try:
                    account_info = client.get_account()
                    rate_limiter.record_request('account', weight=10)
                    consecutive_errors = 0  # Reset on success
                    
                except BinanceAPIException as e:
                    consecutive_errors += 1
                    
                    # Handle specific rate limit errors
                    if e.code == -1003:  # Too many requests
                        logger.error(f'Rate limit exceeded in balance streaming: {e}')
                        if rate_limiter:
                            retry_after = 600  # 10 minutes for streaming
                            rate_limiter.handle_rate_limit_error(e, retry_after)
                        time.sleep(600)  # Wait 10 minutes
                        continue
                    elif e.code == -1021:  # Timestamp outside recv window
                        logger.error(f'Timestamp error in balance streaming: {e}')
                        time.sleep(30)
                        continue
                    else:
                        logger.error(f'Binance API error in balance streaming: {e}')
                        
                    # Exponential backoff for consecutive errors
                    if consecutive_errors >= max_consecutive_errors:
                        logger.error(f'Too many consecutive errors ({consecutive_errors}), suspending balance streaming for 5 minutes')
                        time.sleep(300)
                        consecutive_errors = 0
                    else:
                        time.sleep(min(30 * consecutive_errors, 300))  # Max 5 min
                    continue
                
                balances = []
                total_btc_value = 0.0
                
                for balance in account_info['balances']:
                    free = float(balance['free'])
                    locked = float(balance['locked'])
                    total = free + locked
                    
                    if total > 0:
                        asset = balance['asset']
                        btc_value = 0.0
                        
                        if asset == 'BTC':
                            btc_value = total
                        elif asset != 'BTC':
                            try:
                                if asset == 'USDT':
                                    if client is None:
                                        continue
                                    btc_price = float(client.get_symbol_ticker(symbol='BTCUSDT')['price'])
                                    btc_value = total / btc_price
                                else:
                                    try:
                                        if client is None:
                                            btc_value = 0.0
                                        else:
                                            ticker = client.get_symbol_ticker(symbol=f'{asset}BTC')
                                            btc_value = total * float(ticker['price'])
                                    except:
                                        try:
                                            if client is None:
                                                btc_value = 0.0
                                            else:
                                                ticker = client.get_symbol_ticker(symbol=f'BTC{asset}')
                                                btc_value = total / float(ticker['price'])
                                        except:
                                            btc_value = 0.0
                            except Exception as e:
                                logger.error(f'Error calculating BTC value for {asset}: {e}')
                        
                        total_btc_value += btc_value
                        
                        balances.append({
                            'asset': asset,
                            'free': free,
                            'locked': locked,
                            'total': total,
                            'btc_value': btc_value
                        })
                
                # Get BTC price in USDT for portfolio value
                try:
                    if not rate_limiter.is_rate_limited('ticker'):
                        btc_usdt_price = float(client.get_symbol_ticker(symbol='BTCUSDT')['price'])
                        rate_limiter.record_request('ticker')
                        total_usdt_value = total_btc_value * btc_usdt_price
                    else:
                        logger.warning("Rate limited, using cached BTC price")
                        total_usdt_value = 0.0
                except BinanceAPIException as e:
                    logger.error(f'Error getting BTC price: {e}')
                    if e.code == -1003:
                        rate_limiter.handle_rate_limit_error(e, 300)
                    total_usdt_value = 0.0
                except Exception as e:
                    logger.error(f'Unexpected error getting BTC price: {e}')
                    total_usdt_value = 0.0
                
                balance_data = {
                    'balances': balances,
                    'total_btc_value': total_btc_value,
                    'total_usdt_value': total_usdt_value,
                    'timestamp': datetime.now().isoformat()
                }
                
                socketio.emit('balance_update', balance_data)
                time.sleep(5)  # Update every 5 seconds
            except Exception as e:
                logger.error(f'Error in balance streaming: {e}')
                time.sleep(10)
    
    threading.Thread(target=stream_balance, daemon=True).start()

def start_pnl_streaming():
    def stream_pnl():
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while True:
            try:
                # Check for IP ban first - this is critical
                if rate_limiter and rate_limiter.banned_until:
                    current_time = time.time()
                    if current_time < rate_limiter.banned_until:
                        wait_time = rate_limiter.banned_until - current_time
                        ban_time = datetime.fromtimestamp(rate_limiter.banned_until)
                        logger.warning(f"API banned until {ban_time}, PnL streaming suspended for {wait_time:.0f}s")
                        time.sleep(min(wait_time, 600))  # Max 10 min wait per cycle
                        continue
                
                # Get account info for unrealized PnL
                if client is None:
                    logger.error("Binance client not initialized")
                    time.sleep(30)
                    continue
                
                # Check rate limits before making API call
                if rate_limiter.is_rate_limited('account', weight=10):
                    logger.warning("Rate limited, skipping PnL update")
                    time.sleep(60)  # Increased wait time
                    continue
                
                # Make API call with proper error handling
                try:
                    account_info = client.get_account()
                    rate_limiter.record_request('account', weight=10)
                    consecutive_errors = 0  # Reset on success
                    
                except BinanceAPIException as e:
                    consecutive_errors += 1
                    
                    # Handle specific rate limit errors
                    if e.code == -1003:  # Too many requests
                        logger.error(f'Rate limit exceeded in PnL streaming: {e}')
                        if rate_limiter:
                            retry_after = 600  # 10 minutes for streaming
                            rate_limiter.handle_rate_limit_error(e, retry_after)
                        time.sleep(600)  # Wait 10 minutes
                        continue
                    elif e.code == -1021:  # Timestamp outside recv window
                        logger.error(f'Timestamp error in PnL streaming: {e}')
                        time.sleep(30)
                        continue
                    else:
                        logger.error(f'Binance API error in PnL streaming: {e}')
                        
                    # Exponential backoff for consecutive errors
                    if consecutive_errors >= max_consecutive_errors:
                        logger.error(f'Too many consecutive errors ({consecutive_errors}), suspending PnL streaming for 5 minutes')
                        time.sleep(300)
                        consecutive_errors = 0
                    else:
                        time.sleep(min(30 * consecutive_errors, 300))  # Max 5 min
                    continue
                
                # Calculate unrealized PnL from current positions
                unrealized_pnl = 0.0
                positions = []
                balance_data = {}  # Initialize balance_data dictionary
                
                for balance in account_info['balances']:
                    free = float(balance['free'])
                    locked = float(balance['locked'])
                    total = free + locked
                    
                    if total > 0 and balance['asset'] not in ['USDT', 'USD', 'USDC']:
                        asset = balance['asset']
                        try:
                            # Get current price with rate limiting
                            if not rate_limiter.is_rate_limited('ticker'):
                                try:
                                    if asset == 'BTC':
                                        current_price = float(client.get_symbol_ticker(symbol='BTCUSDT')['price'])
                                    else:
                                        current_price = float(client.get_symbol_ticker(symbol=f'{asset}USDT')['price'])
                                    rate_limiter.record_request('ticker')
                                except BinanceAPIException as e:
                                    logger.error(f'Error getting price for {asset}: {e}')
                                    if e.code == -1003:
                                        rate_limiter.handle_rate_limit_error(e, 300)
                                    continue  # Skip this asset
                                except Exception as e:
                                    logger.error(f'Unexpected error getting price for {asset}: {e}')
                                    continue  # Skip this asset
                            else:
                                logger.warning(f"Rate limited, skipping price for {asset}")
                                continue  # Skip this asset
                            
                            # Calculate real entry price from trade history
                            try:
                                symbol_name = f'{asset}USDT'
                                
                                # Check rate limits for trade history
                                if not rate_limiter.is_rate_limited('trades', weight=10):
                                    try:
                                        trades = client.get_my_trades(symbol=symbol_name, limit=500)
                                        rate_limiter.record_request('trades', weight=10)
                                    except BinanceAPIException as e:
                                        logger.error(f'Error getting trades for {asset}: {e}')
                                        if e.code == -1003:
                                            rate_limiter.handle_rate_limit_error(e, 600)
                                        trades = []  # Use empty trades list
                                    except Exception as e:
                                        logger.error(f'Unexpected error getting trades for {asset}: {e}')
                                        trades = []  # Use empty trades list
                                else:
                                    logger.warning(f"Rate limited, skipping trade history for {asset}")
                                    trades = []  # Use empty trades list
                                
                                if trades:
                                    total_qty = 0
                                    total_cost = 0
                                    for trade in trades:
                                        if trade['isBuyer']:  # Buy orders
                                            qty = float(trade['qty'])
                                            price = float(trade['price'])
                                            total_qty += qty
                                            total_cost += qty * price
                                        else:  # Sell orders
                                            qty = float(trade['qty'])
                                            total_qty -= qty
                                            total_cost -= qty * price
                                    
                                    if total_qty > 0:
                                        avg_entry_price = total_cost / total_qty
                                        unrealized_pnl = (current_price - avg_entry_price) * total_qty
                                        balance_data[asset]['unrealized_pnl'] = unrealized_pnl
                                        balance_data[asset]['entry_price'] = avg_entry_price
                                    
                            except Exception as e:
                                logger.error(f"Error calculating PnL for {asset}: {e}")
                                balance_data[asset]['unrealized_pnl'] = 0.0
                                balance_data[asset]['entry_price'] = current_price
                        
                        except Exception as e:
                            logger.error(f"Error getting price for {asset}: {e}")
                            current_price = 0.0
                        
                        balance_data[asset] = {
                            'free': free,
                            'locked': locked,
                            'total': total,
                            'current_price': current_price,
                            'value_usdt': total * current_price,
                            'unrealized_pnl': balance_data.get(asset, {}).get('unrealized_pnl', 0.0),
                            'entry_price': balance_data.get(asset, {}).get('entry_price', current_price)
                        }
                
                # Emit the PnL data
                socketio.emit('pnl_update', balance_data)
                time.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                logger.error(f'Error in PnL streaming: {e}')
                time.sleep(10)
    
    threading.Thread(target=stream_pnl, daemon=True).start()

def calculate_fifo_pnl(trades, current_quantity, current_price):
    """
    Calculate realized and unrealized P&L using FIFO (First In, First Out) method
    with proper commission handling according to accounting standards.
    """
    if not trades:
        return 0.0, 0.0, 0.0
    
    # Sort trades by time (oldest first for FIFO)
    sorted_trades = sorted(trades, key=lambda x: x['time'])
    
    # Track inventory using FIFO with cost basis including commission
    inventory = []  # List of (quantity, cost_basis_per_unit) tuples
    realized_pnl = 0.0
    
    for trade in sorted_trades:
        qty = float(trade['qty'])
        price = float(trade['price'])
        commission = float(trade['commission'])
        
        if trade['isBuyer']:  # Buy order - add to inventory
            # For buys: commission increases the cost basis
            cost_basis_per_unit = price + (commission / qty)
            inventory.append((qty, cost_basis_per_unit))
            
        else:  # Sell order - remove from inventory using FIFO
            remaining_to_sell = qty
            sell_commission = commission
            
            while remaining_to_sell > 0 and inventory:
                inv_qty, inv_cost_basis = inventory[0]
                
                if inv_qty <= remaining_to_sell:
                    # Sell entire inventory lot
                    sell_qty = inv_qty
                    # Allocate commission proportionally
                    proportional_commission = sell_commission * (sell_qty / qty)
                    
                    realized_pnl += sell_qty * (price - inv_cost_basis) - proportional_commission
                    remaining_to_sell -= inv_qty
                    inventory.pop(0)
                    
                else:
                    # Partial sale of inventory lot
                    sell_qty = remaining_to_sell
                    # Allocate commission proportionally
                    proportional_commission = sell_commission * (sell_qty / qty)
                    
                    realized_pnl += sell_qty * (price - inv_cost_basis) - proportional_commission
                    inventory[0] = (inv_qty - remaining_to_sell, inv_cost_basis)
                    remaining_to_sell = 0
    
    # Calculate average cost basis from remaining inventory
    if inventory:
        total_cost = sum(qty * cost_basis for qty, cost_basis in inventory)
        total_qty = sum(qty for qty, cost_basis in inventory)
        avg_cost_basis = total_cost / total_qty if total_qty > 0 else 0.0
        
        # Calculate unrealized P&L
        unrealized_pnl = current_quantity * (current_price - avg_cost_basis)
    else:
        avg_cost_basis = 0.0
        unrealized_pnl = 0.0
    
    return realized_pnl, unrealized_pnl, avg_cost_basis

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    print("\n\n=== Graceful Shutdown Initiated ===")
    logger.info(f"Received signal {signum}, shutting down gracefully...")
    
    # Close WebSocket connections
    if 'ws_manager' in globals() and ws_manager:
        try:
            ws_manager.disconnect()
            logger.info("WebSocket connections closed")
        except Exception as e:
            logger.error(f"Error closing WebSocket connections: {e}")
    
    # Close trading bot connections
    if 'trading_bot' in globals() and trading_bot:
        try:
            if hasattr(trading_bot, 'client') and trading_bot.client:
                # Close any open connections in the trading bot
                logger.info("Trading bot connections closed")
        except Exception as e:
            logger.error(f"Error closing trading bot connections: {e}")
    
    print("Server shutdown complete. Goodbye!")
    import sys
    sys.exit(0)

if __name__ == '__main__':
    import signal
    
    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Termination signal
    
    port = int(os.environ.get('PORT', 5000))
    
    print(f"=== TradingView Webhook Bot ===")
    print(f"Port: {port}")
    print(f"Host: 0.0.0.0")
    print(f"Press Ctrl+C for graceful shutdown")
    print()
    
    # Initialize services before starting server
    print("Initializing services...")
    if not initialize_app_services():
        print("WARNING: Service initialization had issues, but continuing...")
    else:
        print("Services initialized successfully")
    
    print()
    print("Starting SocketIO server...")
    print(f"Server will be available at:")
    print(f"  http://localhost:{port}")
    print(f"  http://127.0.0.1:{port}")
    print()
    
    try:
        socketio.run(app, host='0.0.0.0', port=port, debug=False)
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        import sys
        sys.exit(1)