{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed"}, "dependencies": {"@tanstack/react-query": "^5.87.4", "axios": "^1.12.1", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "react": "^19.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@playwright/test": "^1.55.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.13", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.13", "@tanstack/react-query-devtools": "^5.87.4", "@types/node": "^24.4.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "playwright": "^1.55.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.13", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}