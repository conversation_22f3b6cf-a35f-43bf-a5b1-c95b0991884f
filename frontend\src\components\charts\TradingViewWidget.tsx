/**
 * TradingView Widget Component
 * Embeds TradingView charts for real-time market data visualization
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { ExternalLink, Settings } from 'lucide-react';
import { useTheme } from '../../hooks/useTheme';

interface TradingViewWidgetProps {
  symbol?: string;
  theme?: 'light' | 'dark';
  interval?: string;
  height?: number;
  width?: string;
  className?: string;
  showToolbar?: boolean;
  autosize?: boolean;
  timezone?: string;
  style?: string;
  locale?: string;
  toolbar_bg?: string;
  enable_publishing?: boolean;
  allow_symbol_change?: boolean;
  details?: boolean;
  hotlist?: boolean;
  calendar?: boolean;
  studies?: string[];
  container_id?: string;
}

// Declare TradingView global type
interface TradingViewWidget {
  remove(): void;
  destroy(): void;
  _iframe?: HTMLIFrameElement;
  iframe?: HTMLIFrameElement;
}

interface TradingView {
  widget: new (config: unknown) => TradingViewWidget;
}

declare global {
  interface Window {
    TradingView: TradingView;
  }
}

/**
 * TradingView advanced chart widget with customizable settings
 */
const TradingViewWidget: React.FC<TradingViewWidgetProps> = ({
  symbol = 'BINANCE:BTCUSDT',
  theme,
  interval = '15',
  height = 400,
  width = '100%',
  className = '',
  showToolbar = true,
  autosize = true,
  timezone = 'Etc/UTC',
  style = '1',
  locale = 'en',
  toolbar_bg = '#f1f3f6',
  enable_publishing = false,
  allow_symbol_change = true,
  details = true,
  hotlist = true,
  calendar = false,
  studies = [],
  container_id
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { theme: systemTheme } = useTheme();
  const [currentInterval, setCurrentInterval] = useState(interval);
  const widgetRef = useRef<TradingViewWidget | null>(null);
  const scriptLoadedRef = useRef<boolean>(false);
  const mountedRef = useRef<boolean>(true);
  const initTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const containerIdRef = useRef<string | null>(null);
  const cleanupInProgressRef = useRef<boolean>(false);
  const widgetErrorHistory = useRef<Array<{ timestamp: number; error: string; context: string }>>([]);
  
  // Use provided theme or fall back to system theme
  const effectiveTheme = theme || systemTheme;
  const [currentTheme, setCurrentTheme] = useState(effectiveTheme);
  
  // Update current theme when effective theme changes
  useEffect(() => {
    setCurrentTheme(effectiveTheme);
  }, [effectiveTheme]);
  
  // Cleanup function to properly destroy widget
  const cleanupWidget = useCallback(() => {
    if (cleanupInProgressRef.current) {
      return; // Prevent concurrent cleanup
    }
    
    cleanupInProgressRef.current = true;
    
    try {
      // Clear any pending initialization timeout
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current);
        initTimeoutRef.current = null;
      }
      
      // Destroy widget instance with enhanced error handling
      if (widgetRef.current) {
        try {
          // Check if widget still exists and has cleanup methods
          if (widgetRef.current && typeof widgetRef.current === 'object') {
            // Try TradingView's standard remove method first
            if (typeof widgetRef.current.remove === 'function') {
              // Additional safety check: ensure the widget's DOM element exists
              const widgetElement = widgetRef.current._iframe || widgetRef.current.iframe;
              if (widgetElement && widgetElement.parentNode) {
                widgetRef.current.remove();
              } else {
                console.warn('Widget DOM element already removed, skipping widget.remove()');
              }
            } else if (typeof widgetRef.current.destroy === 'function') {
              widgetRef.current.destroy();
            }
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          const errorContext = {
            timestamp: new Date().toISOString(),
            error: errorMessage,
            context: 'widget_cleanup',
            widgetExists: !!widgetRef.current,
            widgetType: typeof widgetRef.current,
            stack: error instanceof Error ? error.stack : undefined
          };
          
          console.warn('Widget cleanup error (non-critical):', errorContext);
          
          // Log to error history
          widgetErrorHistory.current.push({
            timestamp: Date.now(),
            error: errorMessage,
            context: 'cleanup'
          });
          
          // Keep only last 20 errors
          if (widgetErrorHistory.current.length > 20) {
            widgetErrorHistory.current.shift();
          }
        }
        widgetRef.current = null;
      }
      
      // Enhanced DOM cleanup with multiple safety checks
      if (containerIdRef.current) {
        const widgetContainer = document.getElementById(containerIdRef.current);
        if (widgetContainer) {
          try {
            // Check if parentNode exists and is still in the DOM
            if (widgetContainer.parentNode && 
                widgetContainer.parentNode.nodeType === Node.ELEMENT_NODE &&
                document.contains(widgetContainer.parentNode)) {
              widgetContainer.parentNode.removeChild(widgetContainer);
            } else {
              console.warn('Widget container parentNode not available for removal');
              // Try alternative cleanup methods
              if (widgetContainer.remove && typeof widgetContainer.remove === 'function') {
                widgetContainer.remove();
              } else {
                // Last resort: clear content
                widgetContainer.innerHTML = '';
              }
            }
          } catch (domError) {
            console.warn('DOM cleanup error:', {
              error: domError instanceof Error ? domError.message : String(domError),
              containerId: containerIdRef.current,
              containerExists: !!widgetContainer,
              parentExists: !!widgetContainer.parentNode,
              parentType: widgetContainer.parentNode?.nodeType
            });
            
            // Fallback cleanup strategies
            try {
              if (widgetContainer.remove && typeof widgetContainer.remove === 'function') {
                widgetContainer.remove();
              } else if (widgetContainer.innerHTML !== undefined) {
                widgetContainer.innerHTML = '';
              }
            } catch (fallbackError) {
              console.warn('DOM cleanup fallback failed:', fallbackError);
            }
          }
        }
        containerIdRef.current = null;
      }
      
      // Reset container content as final fallback
      if (containerRef.current) {
        try {
          containerRef.current.innerHTML = '';
        } catch (error) {
          console.warn('Container reset failed:', error);
        }
      }
    } finally {
      cleanupInProgressRef.current = false;
    }
  }, []);

  // Create widget function
  const createWidget = useCallback((containerId: string) => {
    if (!mountedRef.current || !window.TradingView || cleanupInProgressRef.current) {
      return;
    }
    
    // Store the container ID for cleanup
    containerIdRef.current = containerId;
    
    // Ensure container exists
    const container = document.getElementById(containerId);
    if (!container) {
      console.warn('Widget container not found:', containerId);
      return;
    }
    
    try {
      // Clean up any existing widget first
      if (widgetRef.current) {
        try {
          if (typeof widgetRef.current.remove === 'function') {
            widgetRef.current.remove();
          } else if (typeof widgetRef.current.destroy === 'function') {
            widgetRef.current.destroy();
          }
        } catch (error) {
          console.warn('Previous widget cleanup error:', error);
        }
        widgetRef.current = null;
      }
      
      // Create new widget with enhanced safety checks
      const widgetConfig = {
        autosize,
        symbol,
        interval: currentInterval,
        timezone,
        theme: currentTheme,
        style,
        locale,
        toolbar_bg,
        enable_publishing,
        allow_symbol_change,
        details,
        hotlist,
        calendar,
        studies,
        container_id: containerId,
        width: typeof width === 'number' ? width : undefined,
        height: typeof height === 'number' ? height : undefined,
        onChartReady: () => {
          if (mountedRef.current) {
            setIsLoaded(true);
            setError(null);
          }
        }
      };
      
      // Create the widget instance with error handling
      widgetRef.current = new (window.TradingView as { widget: new (config: Record<string, unknown>) => TradingViewWidget }).widget(widgetConfig);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      const errorContext = {
        timestamp: new Date().toISOString(),
        error: errorMessage,
        context: 'widget_creation',
        symbol,
        interval: currentInterval,
        theme: currentTheme,
        containerExists: !!containerRef.current,
        tradingViewLoaded: !!(window as { TradingView?: unknown }).TradingView,
        cleanupInProgress: cleanupInProgressRef.current
      };
      
      console.error('Error creating TradingView widget:', errorContext);
      
      // Log to error history
      widgetErrorHistory.current.push({
        timestamp: Date.now(),
        error: errorMessage,
        context: 'creation'
      });
      
      // Keep only last 20 errors
      if (widgetErrorHistory.current.length > 20) {
        widgetErrorHistory.current.shift();
      }
      
      if (mountedRef.current) {
        setError(errorMessage);
        setIsLoaded(false);
      }
    }
  }, [symbol, currentTheme, currentInterval, autosize, width, height, timezone, style, locale, toolbar_bg, enable_publishing, allow_symbol_change, details, hotlist, calendar, studies]);

  // Load TradingView script
  const loadTradingViewScript = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      if (window.TradingView) {
        resolve();
        return;
      }
      
      if (scriptLoadedRef.current) {
        // Script is already loading, wait for it
        const checkScript = () => {
          if (window.TradingView) {
            resolve();
          } else {
            setTimeout(checkScript, 100);
          }
        };
        checkScript();
        return;
      }
      
      scriptLoadedRef.current = true;
      const script = document.createElement('script');
      script.src = 'https://s3.tradingview.com/tv.js';
      script.async = true;
      script.onload = () => resolve();
      script.onerror = () => {
        scriptLoadedRef.current = false;
        reject(new Error('Failed to load TradingView script'));
      };
      document.head.appendChild(script);
    });
  }, []);
  
  useEffect(() => {
    mountedRef.current = true;
    
    const initializeWidget = async () => {
      if (!mountedRef.current) return;
      
      try {
        setIsLoaded(false);
        setError(null);

        // Ensure container is properly attached to DOM
        if (!containerRef.current || !containerRef.current.isConnected) {
          console.warn('Container is not attached to DOM, skipping widget initialization');
          return;
        }

        // Clean up any existing content first
        cleanupWidget();

        // Generate unique container ID for the widget
        const uniqueId = container_id || `tradingview_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Create a dedicated widget container inside the main container
        if (containerRef.current && containerRef.current.isConnected) {
          // Clear any existing content
          containerRef.current.innerHTML = '';
          
          // Create widget container
          const widgetContainer = document.createElement('div');
          widgetContainer.id = uniqueId;
          widgetContainer.style.width = typeof width === 'number' ? `${width}px` : String(width);
          widgetContainer.style.height = typeof height === 'number' ? `${height}px` : String(height);
          widgetContainer.style.position = 'relative';
          
          // Append to main container
          containerRef.current.appendChild(widgetContainer);
          
          // Store the widget container ID
          containerIdRef.current = uniqueId;
        } else {
          console.warn('Container reference lost during initialization');
          return;
        }

        // Load script and create widget
        await loadTradingViewScript();
        
        // Create widget with additional safety checks
        if (mountedRef.current && containerRef.current && document.getElementById(uniqueId)) {
          // Double-check that we're not in cleanup mode
          if (!cleanupInProgressRef.current) {
            createWidget(uniqueId);
          } else {
            console.warn('Widget initialization skipped - cleanup in progress');
          }
        } else {
          console.warn('Widget container was removed during initialization');
        }
      } catch (err) {
        console.error('Error initializing TradingView widget:', err);
        if (mountedRef.current) {
          setError('Failed to initialize widget');
          setIsLoaded(false);
        }
      }
    };

    // Debounce widget initialization to prevent rapid creation/destruction
    if (initTimeoutRef.current) {
      clearTimeout(initTimeoutRef.current);
    }
    
    initTimeoutRef.current = setTimeout(() => {
      initializeWidget().catch(error => {
        console.error('Error initializing TradingView widget:', error);
        if (mountedRef.current) {
          setError('Failed to load chart');
          setIsLoaded(true);
        }
      });
    }, 150); // 150ms debounce for better stability

    return () => {
      mountedRef.current = false;
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current);
        initTimeoutRef.current = null;
      }
      cleanupWidget();
    };
  }, [symbol, currentTheme, currentInterval, autosize, width, height, timezone, style, locale, toolbar_bg, enable_publishing, allow_symbol_change, details, hotlist, calendar, studies, container_id, createWidget, loadTradingViewScript, cleanupWidget]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      cleanupWidget();
    };
  }, [cleanupWidget]);
  
  const handleIntervalChange = (newInterval: string) => {
    setCurrentInterval(newInterval);
  };
  
  const openInTradingView = () => {
    const tradingViewUrl = `https://www.tradingview.com/chart/?symbol=BINANCE:${symbol.replace('USDT', '')}USDT`;
    window.open(tradingViewUrl, '_blank', 'noopener,noreferrer');
  };
  
  return (
    <div className={`relative bg-white dark:bg-gray-800 rounded-lg overflow-hidden ${className}`}>
      {/* Widget Toolbar */}
      {showToolbar && (
        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
          <div className="flex items-center space-x-4">
            <h4 className="font-semibold text-gray-900 dark:text-white">
              {symbol} Chart
            </h4>
            
            {/* Interval Selector */}
            <div className="flex space-x-1">
              {['1', '5', '15', '60', '240', '1D'].map((int) => (
                <button
                  key={int}
                  onClick={() => handleIntervalChange(int)}
                  className={`px-2 py-1 text-xs rounded ${
                    currentInterval === int
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                      : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                  }`}
                >
                  {int === '1D' ? '1D' : `${int}m`}
                </button>
              ))}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentTheme(currentTheme === 'light' ? 'dark' : 'light')}
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              title="Toggle theme"
            >
              <Settings className="h-4 w-4" />
            </button>
            
            <button
              onClick={openInTradingView}
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              title="Open in TradingView"
            >
              <ExternalLink className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
      
      {/* Widget Container */}
      <div 
        ref={containerRef}
        style={{ height: `${height}px`, width }}
        className="tradingview-widget-container"
      >
        {/* Loading State */}
        {!isLoaded && !error && (
          <div className="flex items-center justify-center h-full bg-gray-50 dark:bg-gray-800">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2" />
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Loading TradingView chart...
              </p>
            </div>
          </div>
        )}
        
        {/* Error State */}
        {error && (
          <div className="flex items-center justify-center h-full bg-gray-50 dark:bg-gray-800">
            <div className="text-center p-6">
              <div className="text-red-500 mb-2">
                <ExternalLink className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {error}
              </p>
              <button
                onClick={openInTradingView}
                className="btn btn-primary text-sm"
              >
                Open in TradingView
              </button>
            </div>
          </div>
        )}
      </div>
      
      {/* TradingView Attribution */}
      <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>Powered by TradingView</span>
          <div className="flex items-center space-x-2">
            <span>Real-time data</span>
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Simplified TradingView widget for smaller spaces
 */
export function TradingViewMiniWidget({
  symbol = 'BTCUSDT',
  theme = 'light',
  className = '',
}: Pick<TradingViewWidgetProps, 'symbol' | 'theme' | 'className'>) {
  const containerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    let isMounted = true;
    let script: HTMLScriptElement | null = null;
    
    // Clear previous widget safely with React-compatible approach
    const clearContainer = () => {
      try {
        // Use textContent = '' instead of removeChild for React compatibility
        container.textContent = '';
        container.removeAttribute('data-tradingview-widget');
      } catch (err) {
        console.warn('Error clearing TradingView mini container:', err);
      }
    };
    
    clearContainer();
    
    const initializeMiniWidget = async () => {
      if (!isMounted || !container) return;
      
      try {
        // Create unique container ID
        const containerId = `tradingview_mini_${symbol.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // Create wrapper div
        const wrapperDiv = document.createElement('div');
        wrapperDiv.id = containerId;
        wrapperDiv.style.height = '100%';
        wrapperDiv.style.width = '100%';
        
        script = document.createElement('script');
        script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-mini-symbol-overview.js';
        script.type = 'text/javascript';
        script.async = true;
        
        const config = {
          symbol: `BINANCE:${symbol}`,
          width: '100%',
          height: '100%',
          locale: 'en',
          dateRange: '12M',
          colorTheme: theme,
          trendLineColor: 'rgba(41, 98, 255, 1)',
          underLineColor: 'rgba(41, 98, 255, 0.3)',
          underLineBottomColor: 'rgba(41, 98, 255, 0)',
          isTransparent: false,
          autosize: true,
          largeChartUrl: '',
          container_id: containerId,
        };
        
        script.innerHTML = JSON.stringify(config);
        
        script.onerror = () => {
          console.warn('Failed to load TradingView mini widget');
        };
        
        container.appendChild(wrapperDiv);
        wrapperDiv.appendChild(script);
        
      } catch (err) {
        console.error('Error initializing TradingView mini widget:', err);
      }
    };
    
    // Initialize with delay to prevent race conditions
    const timeoutId = setTimeout(initializeMiniWidget, 50);
    
    return () => {
      isMounted = false;
      clearTimeout(timeoutId);
      
      if (script) {
        script.onerror = null;
      }
      
      // Safe cleanup
      if (container) {
        try {
          // Use textContent = '' for React compatibility
          container.textContent = '';
          container.removeAttribute('data-tradingview-widget');
        } catch (err) {
          console.warn('Error during TradingView mini cleanup:', err);
        }
      }
    };
  }, [symbol, theme]);
  
  return (
    <div className={`tradingview-widget-container h-32 ${className}`}>
      <div ref={containerRef} className="h-full" />
    </div>
  );
}

export default TradingViewWidget;