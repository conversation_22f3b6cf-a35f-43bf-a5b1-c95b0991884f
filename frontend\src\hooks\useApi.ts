/**
 * Custom React hooks for API data management
 * Uses React Query for caching, synchronization, and error handling
 */

import { useQuery, useMutation, useQueryClient, type UseQueryResult, keepPreviousData } from '@tanstack/react-query';
import { useState, useEffect } from 'react';
import { api } from '../services/api';
import { useApiErrorHandler } from './useErrorHandler';
import type {
  AccountBalance,
  TradeResponse,
  WebhookData,
  SystemStatus,
  TradingConfig,
  TradeHistoryResponse,
  PnLResponse,
} from '../types/api';

// Query Keys
export const QUERY_KEYS = {
  SYSTEM_STATUS: ['system', 'status'],
  ACCOUNT_BALANCES: ['account', 'balances'],
  TRADING_CONFIG: ['trading', 'config'],
  TRADE_HISTORY: ['trades', 'history'],
  PNL_DATA: ['pnl', 'data'],
} as const;

/**
 * Hook for system status with reduced polling to prevent IP bans
 */
export function useSystemStatus(): UseQueryResult<SystemStatus, Error> {
  const { handleApiError } = useApiErrorHandler();
  
  const query = useQuery<SystemStatus, Error>({
    queryKey: QUERY_KEYS.SYSTEM_STATUS,
    queryFn: ({ signal }) => api.getSystemStatus({ signal }),
    refetchInterval: 60000, // Reduced from 30s to 60s to prevent IP bans
    staleTime: 45000, // Consider data stale after 45 seconds
    retry: (failureCount, error) => {
      // Don't retry on 4xx/5xx errors that indicate IP bans
      if (error instanceof Error && 'status' in error) {
        const status = (error as { status: number }).status;
        if (status === 503 || status === 429 || status === 418) {
          console.warn(`[useSystemStatus] Detected rate limit/ban (${status}), stopping retries`);
          return false; // Stop retrying on IP ban indicators
        }
        if (status >= 400 && status < 500) {
          return false;
        }
      }
      return failureCount < 2; // Reduced retry attempts
    },
    retryDelay: (attemptIndex) => Math.min(5000 * 2 ** attemptIndex, 60000), // Longer delays
  });

  useEffect(() => {
    if (query.isError && query.error) {
      handleApiError(query.error, 'System Status');
    }
  }, [query.isError, query.error, handleApiError]);

  return query;
}

/**
 * Hook for account balances with WebSocket-first approach
 */
export function useAccountBalances(): UseQueryResult<AccountBalance[], Error> {
  const { handleApiError } = useApiErrorHandler();
  const { balances: wsBalances, isConnected: wsConnected } = useRealTimeBalances();
  
  const query = useQuery<AccountBalance[], Error>({
    queryKey: QUERY_KEYS.ACCOUNT_BALANCES,
    queryFn: ({ signal }) => api.getAccountBalances({ signal }),
    // Dramatically reduce polling when WebSocket is connected
    refetchInterval: wsConnected ? 300000 : 60000, // 5 minutes if WS connected, 1 minute if not
    staleTime: wsConnected ? 240000 : 30000, // 4 minutes if WS connected, 30 seconds if not
    // Use WebSocket data when available
    initialData: wsBalances.length > 0 ? wsBalances : undefined,
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors (client errors)
      if (error instanceof Error && 'status' in error) {
        const status = (error as { status: number }).status;
        if (status >= 400 && status < 500) {
          return false;
        }
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    select: (data) => {
      // Handle both direct array (from API service) and BalanceResponse object (from WebSocket)
      let balances: AccountBalance[];
      
      if (Array.isArray(data)) {
        balances = data;
      } else if (data && typeof data === 'object' && 'balances' in data) {
         balances = (data as { balances?: AccountBalance[] }).balances || [];
      } else {
        console.warn('[useAccountBalances] Invalid data received:', data);
        return [];
      }
      
      if (!Array.isArray(balances)) {
        console.warn('[useAccountBalances] Balances is not an array:', balances);
        return [];
      }
      
      // Filter out zero balances and sort by value
      return balances
        .filter(balance => parseFloat(balance.free) > 0 || parseFloat(balance.locked) > 0)
        .sort((a, b) => {
          const aTotal = parseFloat(a.free) + parseFloat(a.locked);
          const bTotal = parseFloat(b.free) + parseFloat(b.locked);
          return bTotal - aTotal;
        });
    }
  });

  useEffect(() => {
    if (query.isError && query.error) {
      handleApiError(query.error, 'Account Balances');
    }
  }, [query.isError, query.error, handleApiError]);

  // Return WebSocket data if available and fresh, otherwise REST API data
  const finalData = wsConnected && wsBalances.length > 0 ? wsBalances : (query.data || []);
  
  return {
    ...query,
    data: finalData,
  } as UseQueryResult<AccountBalance[], Error>;
}

/**
 * Hook for trading configuration
 */
export function useTradingConfig(): UseQueryResult<TradingConfig, Error> {
  const { handleApiError } = useApiErrorHandler();
  
  const query = useQuery<TradingConfig, Error>({
    queryKey: QUERY_KEYS.TRADING_CONFIG,
    queryFn: ({ signal }) => api.getTradingConfig({ signal }),
    staleTime: 60000, // Config doesn't change often, 1 minute stale time
    retry: 2,
  });

  useEffect(() => {
    if (query.isError && query.error) {
      handleApiError(query.error, 'Trading Config');
    }
  }, [query.isError, query.error, handleApiError]);

  return query;
}

/**
 * Hook for trade history with pagination
 */
export function useTradeHistory(page: number = 1, limit: number = 50): UseQueryResult<TradeHistoryResponse, Error> {
  const { handleApiError } = useApiErrorHandler();
  
  const query = useQuery<TradeHistoryResponse, Error>({
    queryKey: [...QUERY_KEYS.TRADE_HISTORY, page, limit],
    queryFn: ({ signal }) => api.getTradeHistory(page, limit, { signal }),
    staleTime: 30000, // 30 seconds stale time
    placeholderData: keepPreviousData, // Keep previous data while fetching new page (v5 replacement)
  });

  useEffect(() => {
    if (query.isError && query.error) {
      handleApiError(query.error, 'Trade History');
    }
  }, [query.isError, query.error, handleApiError]);

  return query;
}

/**
 * Hook for P&L data with WebSocket-first approach
 */
export function usePnLData(): UseQueryResult<PnLResponse, Error> {
  const { handleApiError } = useApiErrorHandler();
  const [pnlData, setPnlData] = useState<PnLResponse | undefined>();
  const [wsConnected, setWsConnected] = useState(false);
  
  // Set up WebSocket subscription for P&L data
  useEffect(() => {
    import('../services/websocket').then(({ webSocketService }) => {
      webSocketService.connect().then(() => {
        const unsubscribe = webSocketService.subscribe('pnl', (data) => {
          // Handle both PnLData and PnLData[] from WebSocket
          if (Array.isArray(data)) {
            // Convert PnLData[] to PnLResponse format
            const totalPnL = data.reduce((sum, item) => sum + (item.unrealized_pnl || 0), 0);
            const realizedPnL = data.reduce((sum, item) => sum + (item.realized_pnl || 0), 0);
            setPnlData({
              success: true,
              pnl: data.map(item => ({
                timestamp: new Date().toISOString(),
                realized_pnl: item.realized_pnl || 0,
                unrealized_pnl: item.unrealized_pnl || 0,
                total_pnl: (item.realized_pnl || 0) + (item.unrealized_pnl || 0)
              })),
              totalPnL,
              realized_pnl: realizedPnL,
              unrealized_pnl: totalPnL - realizedPnL,
              timestamp: new Date().toISOString()
            });
          } else {
            // Single PnLData item, wrap in PnLResponse
            setPnlData({
              success: true,
              pnl: [{
                timestamp: new Date().toISOString(),
                realized_pnl: data.realized_pnl || 0,
                unrealized_pnl: data.unrealized_pnl || 0,
                total_pnl: (data.realized_pnl || 0) + (data.unrealized_pnl || 0)
              }],
              totalPnL: data.unrealized_pnl || 0,
              realized_pnl: data.realized_pnl || 0,
              unrealized_pnl: data.unrealized_pnl || 0,
              timestamp: new Date().toISOString()
            });
          }
        });
        
        const unsubscribeConnection = webSocketService.onConnectionChange((connected) => {
          setWsConnected(connected);
        });
        
        webSocketService.send('pnl', { subscribe: true }).catch(console.error);
        
        return () => {
          unsubscribe();
          unsubscribeConnection();
        };
      }).catch(console.error);
    });
  }, []);
  
  const query = useQuery<PnLResponse, Error>({
    queryKey: QUERY_KEYS.PNL_DATA,
    queryFn: ({ signal }) => api.getPnLData({ signal }),
    // Reduce polling significantly when WebSocket is connected
    refetchInterval: wsConnected ? 120000 : 30000, // 2 minutes if WS connected, 30 seconds if not
    staleTime: wsConnected ? 90000 : 15000, // 1.5 minutes if WS connected, 15 seconds if not
    initialData: pnlData,
  });

  useEffect(() => {
    if (query.isError && query.error) {
      handleApiError(query.error, 'P&L Data');
    }
  }, [query.isError, query.error, handleApiError]);

  // Return WebSocket data if available, otherwise REST API data with fallback
  const finalData = wsConnected && pnlData ? pnlData : (query.data || {
    success: false,
    pnl: [],
    totalPnL: 0,
    realized_pnl: 0,
    unrealized_pnl: 0,
    timestamp: new Date().toISOString()
  });
  
  return {
    ...query,
    data: finalData,
  } as UseQueryResult<PnLResponse, Error>;
}

/**
 * Hook for executing trades
 */
export function useExecuteTrade() {
  const queryClient = useQueryClient();
  const { handleApiError } = useApiErrorHandler();
  
  return useMutation({
    mutationFn: (tradeData: WebhookData) => api.executeTrade(tradeData),
    onSuccess: (data: TradeResponse) => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.ACCOUNT_BALANCES });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.TRADE_HISTORY });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.PNL_DATA });
      
      console.log('Trade executed successfully:', data);
    },
    onError: (error) => {
      handleApiError(error, 'Trade Execution');
    },
  });
}

/**
 * Hook for emergency stop toggle
 */
export function useEmergencyStop() {
  const queryClient = useQueryClient();
  const { handleApiError } = useApiErrorHandler();
  
  return useMutation({
    mutationFn: () => api.toggleEmergencyStop(),
    onSuccess: () => {
      // Invalidate trading config to get updated emergency stop status
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.TRADING_CONFIG });
    },
    onError: (error) => {
      handleApiError(error, 'Emergency Stop');
    },
  });
}

/**
 * Hook for API connectivity monitoring
 */
export function useApiStatus() {
  const [isOnline, setIsOnline] = useState(true);
  const [lastChecked, setLastChecked] = useState<Date>(new Date());
  
  useEffect(() => {
    const checkConnection = async () => {
      try {
        const isConnected = await api.testConnection();
        setIsOnline(isConnected);
        setLastChecked(new Date());
      } catch {
        setIsOnline(false);
        setLastChecked(new Date());
      }
    };
    
    // Check immediately
    checkConnection();
    
    // Check every 30 seconds
    const interval = setInterval(checkConnection, 30000);
    
    return () => clearInterval(interval);
  }, []);
  
  return {
    isOnline,
    lastChecked,
    checkConnection: () => api.testConnection(),
  };
}

/**
 * Hook for real-time balance updates using WebSocket
 */
export function useRealTimeBalances() {
  const [balances, setBalances] = useState<AccountBalance[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  
  useEffect(() => {
    // Import WebSocket service dynamically to avoid circular dependencies
    import('../services/websocket').then(({ webSocketService }) => {
      // Connect to WebSocket
      webSocketService.connect().then(() => {
        console.log('[useRealTimeBalances] WebSocket connected');
        setIsConnected(true);
        
        // Subscribe to balance updates
        const unsubscribe = webSocketService.subscribe('balance', (data) => {
          console.log('[useRealTimeBalances] Received balance update:', data);
          setBalances(data);
          setLastUpdate(new Date());
        });
        
        // Subscribe to connection status
        const unsubscribeConnection = webSocketService.onConnectionChange((connected) => {
          setIsConnected(connected);
        });
        
        // Request initial balance data
        webSocketService.send('balance', { subscribe: true }).catch(console.error);
        
        return () => {
          unsubscribe();
          unsubscribeConnection();
        };
      }).catch((error) => {
        console.error('[useRealTimeBalances] WebSocket connection failed:', error);
        setIsConnected(false);
      });
    });
  }, []);
  
  return { 
    balances, 
    isConnected, 
    lastUpdate 
  };
}

/**
 * Hook for managing selected trading pair
 */
export function useTradingPair() {
  const [selectedPair, setSelectedPair] = useState<string>('BTCUSDT');
  type PricePoint = { timestamp: number; price: number; volume: number };
  const [priceData, setPriceData] = useState<PricePoint[]>([]);
  
  // Integrate with real price data from backend API
  useEffect(() => {
    const fetchPriceData = async () => {
      try {
        const response = await fetch(`/api/prices?symbols=${selectedPair}`);
        if (response.ok) {
          const data = await response.json();
          const price = data.prices?.[selectedPair];
          if (price) {
            const newPrice: PricePoint = {
              timestamp: Date.now(),
              price: parseFloat(price),
              volume: Math.random() * 1000, // Volume data would come from WebSocket in production
            };
            setPriceData(prev => [...prev.slice(-99), newPrice]); // Keep last 100 points
          }
        }
      } catch (error) {
        console.error('Failed to fetch price data:', error);
      }
    };
    
    // Initial fetch
    fetchPriceData();
    
    // Update price data every 5 seconds (avoid rate limiting)
    const interval = setInterval(fetchPriceData, 5000);
    
    return () => clearInterval(interval);
  }, [selectedPair]);
  
  return {
    selectedPair,
    setSelectedPair,
    priceData,
  };
}

/**
 * Hook for API connectivity monitoring
 */
export function useApiConnectivity() {
  const [isConnected, setIsConnected] = useState(true);
  const { data: systemStatus } = useSystemStatus();
  
  useEffect(() => {
    const connected = Boolean(systemStatus?.api_connected) && systemStatus?.system_health !== 'error';
    setIsConnected(connected);
  }, [systemStatus]);
  
  return {
    isConnected,
  };
}

/**
 * Hook for error handling and user notifications
 */
export function useErrorHandler() {
  const [errors, setErrors] = useState<string[]>([]);
  
  const addError = (error: string) => {
    setErrors(prev => [...prev, error]);
    
    // Auto-remove error after 5 seconds
    setTimeout(() => {
      setErrors(prev => prev.filter(e => e !== error));
    }, 5000);
  };
  
  const clearErrors = () => setErrors([]);
  
  return {
    errors,
    addError,
    clearErrors,
  };
}