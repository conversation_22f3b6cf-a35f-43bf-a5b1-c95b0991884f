/**
 * Trading Controls Component
 * Provides interface for trading operations and emergency controls
 */

import { useState, useMemo } from 'react';
import { Play, AlertTriangle, Settings, Shield } from 'lucide-react';
import type { TradingConfig, WebhookData } from '../../types/api';
import { useEmergencyStop, useExecuteTrade } from '../../hooks/useApi';
import { InlineSpinner } from '../ui/LoadingSpinner';

interface TradingControlsProps {
  config: TradingConfig | undefined;
  className?: string;
}

/**
 * Trading control panel with emergency stop and configuration display
 */
function TradingControls({ config, className = '' }: TradingControlsProps) {
  const [showConfirmStop, setShowConfirmStop] = useState(false);
  const [testTradeSymbol, setTestTradeSymbol] = useState('BTCUSDT');
  const [testTradeAmount, setTestTradeAmount] = useState('0.001');

  const emergencyStopMutation = useEmergencyStop();
  const executeTestTrade = useExecuteTrade();

  const isTradingEnabled = Boolean(config?.enabled);

  const allowedSymbols = useMemo(() => config?.allowed_symbols ?? [], [config?.allowed_symbols]);

  const extractBaseCoin = (symbol: string): string => {
    const knownQuotes = ['USDT', 'BUSD', 'USDC', 'USD', 'EUR', 'GBP', 'BTC', 'ETH'];
    for (const quote of knownQuotes) {
      if (symbol.endsWith(quote)) return symbol.slice(0, -quote.length);
    }
    // Fallback: assume first 3-4 uppercase letters are base
    const match = symbol.match(/^([A-Z]{3,})([A-Z]+)$/);
    return match ? match[1] : symbol;
  };

  const handleEmergencyStop = async () => {
    try {
      await emergencyStopMutation.mutateAsync();
      setShowConfirmStop(false);
    } catch (error) {
      console.error('Emergency stop failed:', error);
    }
  };

  const handleTestTrade = async () => {
    if (!testTradeSymbol || !testTradeAmount) return;

    try {
      const payload: WebhookData = {
        action: 'BUY',
        symbol: testTradeSymbol,
        coin: extractBaseCoin(testTradeSymbol),
        quantity: testTradeAmount,
      };
      await executeTestTrade.mutateAsync(payload);
    } catch (error) {
      console.error('Test trade failed:', error);
    }
  };

  return (
    <div className={`trading-card ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Settings className="h-5 w-5 text-blue-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Trading Controls
          </h3>
        </div>

        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
          isTradingEnabled
            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
            : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
        }`}>
          {isTradingEnabled ? 'Trading Active' : 'Trading Stopped'}
        </div>
      </div>

      {/* Emergency Stop Section */}
      <div className="mb-6" data-testid="emergency-stop">
        <div className="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800">
          <div className="flex items-center space-x-3">
            <Shield className="h-6 w-6 text-red-500" />
            <div>
              <h4 className="font-semibold text-red-900 dark:text-red-400">
                Emergency Stop
              </h4>
              <p className="text-sm text-red-700 dark:text-red-300">
                Immediately halt all trading operations
              </p>
            </div>
          </div>

          {!showConfirmStop ? (
            <button
              onClick={() => setShowConfirmStop(true)}
              disabled={!isTradingEnabled || emergencyStopMutation.isPending}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {emergencyStopMutation.isPending ? (
                <div className="flex items-center space-x-2">
                  <InlineSpinner size="sm" color="white" />
                  <span>Stopping...</span>
                </div>
              ) : (
                'Emergency Stop'
              )}
            </button>
          ) : (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowConfirmStop(false)}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={handleEmergencyStop}
                disabled={emergencyStopMutation.isPending}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 font-medium text-sm"
              >
                Confirm Stop
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Trading Configuration */}
      <div className="mb-6">
        <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
          Current Configuration
        </h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
              Allowed Symbols
            </p>
            <div className="flex flex-wrap gap-1">
              {allowedSymbols.length > 0 ? (
                allowedSymbols.map((symbol) => (
                  <span
                    key={symbol}
                    className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded"
                  >
                    {symbol}
                  </span>
                ))
              ) : (
                <span className="text-gray-500 text-sm">None configured</span>
              )}
            </div>
          </div>

          <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
              Max Position Size
            </p>
            <p className="font-semibold text-gray-900 dark:text-white">
              {config?.max_position_size ?? 'Not set'}
            </p>
          </div>

          <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
              Risk Percentage
            </p>
            <p className="font-semibold text-gray-900 dark:text-white">
              {config?.risk_percentage}%
            </p>
          </div>

          <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
              Auto Trading
            </p>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                isTradingEnabled ? 'bg-green-500' : 'bg-red-500'
              }`} />
              <span className="font-semibold text-gray-900 dark:text-white">
                {isTradingEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Test Trading Section */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
          Test Trade
        </h4>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Symbol
            </label>
            <select
              value={testTradeSymbol}
              onChange={(e) => setTestTradeSymbol(e.target.value)}
              className="input w-full"
              disabled={!isTradingEnabled}
            >
              {allowedSymbols.length > 0 ? (
                allowedSymbols.map((symbol) => (
                  <option key={symbol} value={symbol}>
                    {symbol}
                  </option>
                ))
              ) : (
                <option value="BTCUSDT">BTCUSDT</option>
              )}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Amount
            </label>
            <input
              type="number"
              value={testTradeAmount}
              onChange={(e) => setTestTradeAmount(e.target.value)}
              step="0.001"
              min="0.001"
              className="input w-full"
              disabled={!isTradingEnabled}
              placeholder="0.001"
            />
          </div>

          <div className="flex items-end">
            <button
              onClick={handleTestTrade}
              disabled={!isTradingEnabled || executeTestTrade.isPending || !testTradeSymbol || !testTradeAmount}
              className="btn btn-primary w-full"
            >
              {executeTestTrade.isPending ? (
                <div className="flex items-center space-x-2">
                  <InlineSpinner size="sm" color="white" />
                  <span>Executing...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Play className="h-4 w-4" />
                  <span>Test Buy</span>
                </div>
              )}
            </button>
          </div>
        </div>

        <div className="text-sm text-gray-600 dark:text-gray-400">
          <p className="flex items-center space-x-1">
            <AlertTriangle className="h-4 w-4" />
            <span>Test trades use real funds. Use small amounts for testing.</span>
          </p>
        </div>
      </div>

      {/* Status Messages */}
      {emergencyStopMutation.error && (
        <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-800 dark:text-red-400 text-sm">
            Emergency stop failed: {String((emergencyStopMutation.error as Error)?.message || emergencyStopMutation.error)}
          </p>
        </div>
      )}

      {executeTestTrade.error && (
        <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-800 dark:text-red-400 text-sm">
            Test trade failed: {String((executeTestTrade.error as Error)?.message || executeTestTrade.error)}
          </p>
        </div>
      )}

      {executeTestTrade.isSuccess && (
        <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <p className="text-green-800 dark:text-green-400 text-sm">
            Test trade executed successfully!
          </p>
        </div>
      )}
    </div>
  );
}

export default TradingControls;