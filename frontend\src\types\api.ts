/**
 * TypeScript definitions for TradingView Backend API
 * Synchronized with Flask backend data models
 */

// Account Balance Types
export interface AccountBalance {
  asset: string;
  free: string;
  locked: string;
}

export interface BalanceResponse {
  status?: 'success' | 'error';
  balances: AccountBalance[];
  total_btc_value?: number;
  total_usdt_value?: number;
  message?: string;
  error?: string;
  timestamp: string;
}

// Trade Order Types
export interface TradeOrder {
  success: boolean;
  order_id?: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  quantity: string;
  status?: string;
  market_price?: string;
}

export interface TradeResponse {
  status: 'success' | 'error';
  message: string;
  order_details?: TradeOrder;
  market_price?: string;
  error?: string;
  timestamp: string;
}

// Webhook Data Types
export interface WebhookData {
  action: 'BUY' | 'SELL';
  symbol: string;
  coin: string;
  quantity?: string;
}

// Configuration Types
export interface TradingConfiguration {
  enabled: boolean;
  emergency_stop: boolean;
  max_position_size: number;
  risk_percentage: number;
  stop_loss_percentage: number | string;
  take_profit_percentage: number | string;
  allowed_symbols: string[];
  quantity_percentage: number;
  max_quantities: Record<string, number>;
  fixed_quantities: Record<string, number>;
  timestamp: string;
}

export interface ConfigResponse {
  status: 'success' | 'error';
  config?: TradingConfiguration;
  message?: string;
  error?: string;
  timestamp: string;
}

// Alias for backward compatibility
export type TradingConfig = TradingConfiguration;

// System Status Types
export interface SystemStatus {
  api_connected: boolean;
  websocket_connected: boolean;
  last_heartbeat: string;
  trading_enabled: boolean;
  system_health: 'healthy' | 'warning' | 'error';
  uptime: number;
}

// Error Types
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
}

// Price Data Types
export interface PriceData {
  symbol: string;
  price: string;
  timestamp: string;
}

export interface PriceResponse {
  status: 'success' | 'error';
  data?: PriceData[];
  message?: string;
  error?: string;
  timestamp: string;
}

// Portfolio Types
export interface PortfolioItem {
  symbol: string;
  quantity: number;
  average_price: number;
  current_price: number;
  unrealized_pnl: number;
  percentage_change: number;
}

export interface PortfolioResponse {
  status: 'success' | 'error';
  portfolio?: PortfolioItem[];
  total_value?: number;
  total_pnl?: number;
  message?: string;
  error?: string;
  timestamp: string;
}

// Trade History Types
export interface TradeHistoryItem {
  id: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  quantity: string;
  price: string;
  timestamp: string;
  status: 'FILLED' | 'PARTIALLY_FILLED' | 'CANCELLED' | 'PENDING';
  commission?: string;
  commission_asset?: string;
}

export interface TradeHistoryResponse {
  status: 'success' | 'error';
  trades?: TradeHistoryItem[];
  total_count?: number;
  message?: string;
  error?: string;
  timestamp: string;
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: string;
  data: unknown;
  timestamp: string;
}

export interface PriceUpdateMessage extends WebSocketMessage {
  type: 'price_update';
  data: {
    symbol: string;
    price: number;
    change_24h: number;
    volume_24h: number;
  };
}

export interface PnLUpdateMessage extends WebSocketMessage {
  type: 'pnl_update';
  data: BalancePnLData; // [asset: string]: AssetBalancePnL
}

export interface TradeUpdateMessage extends WebSocketMessage {
  type: 'trade_update';
  data: TradeHistoryItem;
}

export interface SystemUpdateMessage extends WebSocketMessage {
  type: 'system_update';
  data: SystemStatus;
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  status: 'success' | 'error';
  data?: T[];
  pagination?: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
  };
  message?: string;
  error?: string;
  timestamp: string;
}

export interface TradeHistoryParams extends PaginationParams {
  symbol?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
}

// Asset Balance P&L Types (matches backend structure)
export interface AssetBalancePnL {
  free: string;
  locked: string;
  total: string;
  current_price: number;
  value_usdt: number;
  unrealized_pnl: number;
  realized_pnl: number;
  entry_price: number;
}

export interface BalancePnLData {
  [asset: string]: AssetBalancePnL;
}

// Historical P&L Types
export interface PnLHistoryItem {
  timestamp: string;
  realized_pnl: number;
  unrealized_pnl: number;
  total_pnl: number;
}

export interface PnLResponse {
  success: boolean;
  pnl: PnLHistoryItem[];
  totalPnL: number;
  realized_pnl: number;
  unrealized_pnl: number;
  timestamp: string;
}

// Legacy P&L Types (for backward compatibility)
export interface PnLData {
  total_pnl: number;
  unrealized_pnl: number;
  realized_pnl: number;
  portfolio_value: number;
  daily_change: number;
  daily_change_percentage: number;
}
