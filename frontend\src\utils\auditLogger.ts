/**
 * Comprehensive Audit Logger for TradingView Dashboard
 * Tracks all data source changes, API integrations, and production data implementations
 */

export interface AuditLogEntry {
  timestamp: string;
  action: string;
  component: string;
  dataSource: 'mock' | 'live' | 'hybrid';
  description: string;
  changes: string[];
  verification: {
    status: 'pending' | 'verified' | 'failed';
    details: string;
  };
  metadata?: Record<string, unknown>;
}

class AuditLogger {
  private logs: AuditLogEntry[] = [];
  private readonly storageKey = 'tradingview_audit_log';
  private readonly maxLogs = 100; // Limit to 100 entries to prevent localStorage overflow
  private readonly maxStorageSize = 2 * 1024 * 1024; // 2MB limit

  constructor() {
    this.loadLogs();
  }

  /**
   * Log a new audit entry with timestamp and detailed tracking
   */
  log(entry: Omit<AuditLogEntry, 'timestamp'>): void {
    const auditEntry: AuditLogEntry = {
      ...entry,
      timestamp: new Date().toISOString(),
    };

    this.logs.push(auditEntry);
    
    // Maintain size limits to prevent localStorage overflow
    this.maintainSizeLimits();
    
    this.saveLogs();
    
    // Also log to console in development
    if (import.meta.env.MODE === 'development') {
      console.group(`🔍 AUDIT LOG: ${entry.action}`);
      console.log('Component:', entry.component);
      console.log('Data Source:', entry.dataSource);
      console.log('Description:', entry.description);
      console.log('Changes:', entry.changes);
      console.log('Verification:', entry.verification);
      console.log('Timestamp:', auditEntry.timestamp);
      console.groupEnd();
    }
  }

  /**
   * Get all audit logs with optional filtering
   */
  getLogs(filter?: {
    component?: string;
    dataSource?: AuditLogEntry['dataSource'];
    action?: string;
    dateRange?: { start: Date; end: Date };
  }): AuditLogEntry[] {
    let filteredLogs = [...this.logs];

    if (filter) {
      if (filter.component) {
        filteredLogs = filteredLogs.filter(log => 
          log.component.toLowerCase().includes(filter.component!.toLowerCase())
        );
      }
      
      if (filter.dataSource) {
        filteredLogs = filteredLogs.filter(log => log.dataSource === filter.dataSource);
      }
      
      if (filter.action) {
        filteredLogs = filteredLogs.filter(log => 
          log.action.toLowerCase().includes(filter.action!.toLowerCase())
        );
      }
      
      if (filter.dateRange) {
        filteredLogs = filteredLogs.filter(log => {
          const logDate = new Date(log.timestamp);
          return logDate >= filter.dateRange!.start && logDate <= filter.dateRange!.end;
        });
      }
    }

    return filteredLogs.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }

  /**
   * Generate comprehensive audit report
   */
  generateReport(): {
    summary: {
      totalEntries: number;
      mockDataRemoved: number;
      liveDataImplemented: number;
      pendingVerifications: number;
      failedVerifications: number;
    };
    componentStatus: Record<string, {
      dataSource: AuditLogEntry['dataSource'];
      lastUpdated: string;
      verificationStatus: 'pending' | 'verified' | 'failed';
    }>;
    timeline: AuditLogEntry[];
  } {
    const mockDataRemoved = this.logs.filter(log => 
      log.action.toLowerCase().includes('remove') && 
      log.description.toLowerCase().includes('mock')
    ).length;

    const liveDataImplemented = this.logs.filter(log => 
      log.dataSource === 'live'
    ).length;

    const pendingVerifications = this.logs.filter(log => 
      log.verification.status === 'pending'
    ).length;

    const failedVerifications = this.logs.filter(log => 
      log.verification.status === 'failed'
    ).length;

    // Group by component to get latest status
    const componentStatus: Record<string, { dataSource: AuditLogEntry['dataSource']; lastUpdated: string; verificationStatus: 'pending' | 'verified' | 'failed' }> = {};
    this.logs.forEach(log => {
      const existing = componentStatus[log.component];
      if (!existing || new Date(log.timestamp) > new Date(existing.lastUpdated)) {
        componentStatus[log.component] = {
          dataSource: log.dataSource,
          lastUpdated: log.timestamp,
          verificationStatus: log.verification.status,
        };
      }
    });

    return {
      summary: {
        totalEntries: this.logs.length,
        mockDataRemoved,
        liveDataImplemented,
        pendingVerifications,
        failedVerifications,
      },
      componentStatus,
      timeline: this.getLogs(),
    };
  }

  /**
   * Export audit log as JSON for external analysis
   */
  exportLog(): string {
    return JSON.stringify({
      exportDate: new Date().toISOString(),
      report: this.generateReport(),
      rawLogs: this.logs,
    }, null, 2);
  }

  /**
   * Clear all audit logs (use with caution)
   */
  clearLogs(): void {
    this.logs = [];
    this.saveLogs();
  }

  private loadLogs(): void {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        this.logs = JSON.parse(stored) as AuditLogEntry[];
      }
    } catch (error) {
      console.warn('Failed to load audit logs from localStorage:', error);
      this.logs = [];
    }
  }

  /**
   * Maintain size limits to prevent localStorage overflow
   */
  private maintainSizeLimits(): void {
    // Remove oldest entries if we exceed the maximum count
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
    
    // Check storage size and reduce if necessary
    const dataSize = JSON.stringify(this.logs).length;
    if (dataSize > this.maxStorageSize) {
      // Remove oldest 25% of entries
      const removeCount = Math.floor(this.logs.length * 0.25);
      this.logs = this.logs.slice(removeCount);
    }
  }

  /**
   * Get estimated storage usage
   */
  getStorageInfo(): { size: number; count: number; maxSize: number; maxCount: number } {
    return {
      size: JSON.stringify(this.logs).length,
      count: this.logs.length,
      maxSize: this.maxStorageSize,
      maxCount: this.maxLogs,
    };
  }

  private saveLogs(): void {
    try {
      const dataToStore = JSON.stringify(this.logs);
      
      // Check if data exceeds localStorage quota
      if (dataToStore.length > this.maxStorageSize) {
        console.warn('Audit log data exceeds size limit, trimming...');
        this.maintainSizeLimits();
        localStorage.setItem(this.storageKey, JSON.stringify(this.logs));
      } else {
        localStorage.setItem(this.storageKey, dataToStore);
      }
    } catch (error) {
      if (error instanceof DOMException && error.name === 'QuotaExceededError') {
        console.warn('localStorage quota exceeded, clearing old audit logs...');
        // Emergency cleanup - keep only the last 20 entries
        this.logs = this.logs.slice(-20);
        try {
          localStorage.setItem(this.storageKey, JSON.stringify(this.logs));
        } catch (secondError) {
          console.error('Failed to save even after cleanup:', secondError);
          // Clear all logs as last resort
          this.logs = [];
          localStorage.removeItem(this.storageKey);
        }
      } else {
        console.warn('Failed to save audit logs to localStorage:', error);
      }
    }
  }
}

// Export singleton instance
export const auditLogger = new AuditLogger();

// Initialize audit logging session
auditLogger.log({
  action: 'AUDIT_SESSION_START',
  component: 'AuditLogger',
  dataSource: 'live',
  description: 'Started comprehensive audit logging for production data migration',
  changes: ['Initialized audit logging system', 'Created comprehensive tracking infrastructure'],
  verification: {
    status: 'verified',
    details: 'Audit logger successfully initialized and ready for tracking',
  },
});