/**
 * Error Handling Hook
 * Provides centralized error handling with logging and user notifications
 */

import { useState, useCallback } from 'react';
import axios, { type AxiosError } from 'axios';

interface ErrorState {
  error: Error | null;
  isError: boolean;
  errorMessage: string | null;
  errorCode: string | null;
  timestamp: Date | null;
}

interface ErrorHandlerOptions {
  logErrors?: boolean;
  showNotifications?: boolean;
  retryable?: boolean;
  maxRetries?: number;
  onError?: (error: Error) => void;
}

interface ApiError extends Error {
  status?: number;
  code?: string;
  details?: unknown;
}

/**
 * Main error handling hook
 */
export function useErrorHandler(options: ErrorHandlerOptions = {}) {
  const {
    logErrors = true,
    retryable = false,
    maxRetries = 3,
    onError,
  } = options;

  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    isError: false,
    errorMessage: null,
    errorCode: null,
    timestamp: null,
  });

  const [retryCount, setRetryCount] = useState(0);

  /**
   * Handle error with comprehensive logging and state management
   */
  const handleError = useCallback((error: Error | ApiError, context?: string) => {
    const timestamp = new Date();
    // Format error message based on error type
    let errorMessage = error.message || 'An unexpected error occurred';
    
    // Handle API service unavailable errors
    if ('status' in error && (error as ApiError).status === 503) {
      errorMessage = 'Trading service is temporarily unavailable due to API restrictions. Please try again later.';
    }
    const errorCode = 'code' in error ? (error as ApiError).code ?? null : null;

    // Update error state
    setErrorState({
      error,
      isError: true,
      errorMessage,
      errorCode,
      timestamp,
    });

    // Log error if enabled
    if (logErrors) {
      console.error('[Error Handler]', {
        message: errorMessage,
        code: errorCode,
        context,
        timestamp: timestamp.toISOString(),
        stack: error.stack,
        ...(('status' in error && (error as ApiError).status !== undefined) ? { status: (error as ApiError).status } : {}),
        ...(('details' in error && (error as ApiError).details !== undefined) ? { details: (error as ApiError).details } : {}),
      });
    }

    // Call custom error handler if provided
    onError?.(error);

    // Send error to monitoring service (in production)
    if (import.meta.env.PROD) {
      // This would integrate with services like Sentry, LogRocket, etc.
      // sendErrorToMonitoring(error, context);
    }
  }, [logErrors, onError]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      isError: false,
      errorMessage: null,
      errorCode: null,
      timestamp: null,
    });
    setRetryCount(0);
  }, []);

  /**
   * Retry failed operation
   */
  const retry = useCallback(() => {
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1);
      clearError();
      return true;
    }
    return false;
  }, [retryCount, maxRetries, clearError]);

  /**
   * Check if error is retryable
   */
  const isRetryable = useCallback((error: unknown): boolean => {
    if (!retryable) return false;

    if (axios.isAxiosError(error)) {
      const err = error as AxiosError;
      const status = err.response?.status;
      // Retry on 5xx and network errors
      return !status || (status >= 500 && status < 600);
    }

    return false;
  }, [retryable]);

  return {
    errorState,
    handleError,
    clearError,
    retry,
    isRetryable,
  };
}

/**
 * Thin wrapper around useErrorHandler for API-specific contexts
 */
export function useApiErrorHandler() {
  const { handleError } = useErrorHandler({ logErrors: true });

  const handleApiError = useCallback((error: unknown, context?: string) => {
    let normalizedError: Error;
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      normalizedError = new Error(axiosError.message || 'API request failed');
    } else if (error instanceof Error) {
      normalizedError = error;
    } else {
      normalizedError = new Error('Unknown error');
    }

    handleError(normalizedError, context);
  }, [handleError]);

  return { handleApiError };
}

/**
 * WebSocket error handler for real-time connections
 */
export function useWebSocketErrorHandler() {
  const { handleError, clearError } = useErrorHandler({ logErrors: true, retryable: true });

  const handleWebSocketError = useCallback((error: unknown, context?: string) => {
    const err = error instanceof Error ? error : new Error('WebSocket error');
    handleError(err, context);
  }, [handleError]);

  return { handleWebSocketError, clearError };
}

/**
 * Form error handler to map API errors to form field errors
 */
export function useFormErrorHandler() {
  const { handleError } = useErrorHandler({ logErrors: true });

  const mapApiErrorToField = useCallback((error: unknown): Record<string, string> => {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<{ field?: string; message?: string }>;
      const field = axiosError.response?.data?.field ?? 'form';
      const message = axiosError.response?.data?.message ?? axiosError.message ?? 'Invalid input';
      return { [field]: message };
    }
    return { form: 'An unknown error occurred' };
  }, []);

  const handleFormError = useCallback((error: unknown) => {
    const err = error instanceof Error ? error : new Error('Form submission error');
    handleError(err, 'Form');
  }, [handleError]);

  return { mapApiErrorToField, handleFormError };
}

/**
 * Global error handler for app-level error boundaries or listeners
 */
export function useGlobalErrorHandler() {
  const { handleError, clearError, errorState } = useErrorHandler({ logErrors: true });

  const handleGlobalError = useCallback((error: unknown, context?: string) => {
    const err = error instanceof Error ? error : new Error('Unknown global error');
    handleError(err, context);
  }, [handleError]);

  return { handleGlobalError, error: errorState.error, clearError };
}

/** Utility to create a rich ApiError object */
export function createError(
  message: string,
  code?: string,
  status?: number,
  details?: unknown
): ApiError {
  const error = new Error(message) as ApiError;
  if (code) error.code = code;
  if (status) error.status = status;
  if (details) error.details = details;
  return error;
}

/** Determine if an ApiError is retryable based on status */
export function isRetryableError(error: ApiError): boolean {
  if (error.status === undefined) return true; // Network/unknown errors are retryable
  return error.status >= 500 && error.status < 600;
}